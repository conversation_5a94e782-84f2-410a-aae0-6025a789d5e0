import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'
import { performanceMonitor } from '@/utils/performance'

import '@/assets/style/global.scss'
import '@/assets/style/element-theme.scss'

const app = createApp(App)

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

app.use(pinia)
app.use(router)

app.mount('#app')

window.addEventListener('load', () => {
  const metrics = performanceMonitor()
  if (metrics) {
    console.log('📊 页面性能指标:')
    console.table(metrics)

    if (import.meta.env.DEV) {
      // console.log('🚀 性能分析:')
      // console.log(`DNS 查询: ${metrics.dns}ms`)
      // console.log(`TCP 连接: ${metrics.tcp}ms`)
      // console.log(`请求响应: ${metrics.request}ms`)
      // console.log(`DOM 解析: ${metrics.domParse}ms`)
      // console.log(`白屏时间: ${metrics.whiteScreen}ms`)
      // console.log(`DOM 内容加载: ${metrics.domContentLoaded}ms`)
      // console.log(`DOM 解析完成: ${metrics.domReady}ms`)
      // console.log(`首屏时间: ${metrics.firstScreen}ms`)

      // 性能建议
      if (metrics.firstScreen > 3000) {
        console.warn('⚠️ 首屏时间较长，建议优化')
      }
      if (metrics.whiteScreen > 1000) {
        console.warn('⚠️ 白屏时间较长，建议优化')
      }
      if (metrics.domContentLoaded > 2000) {
        console.warn('⚠️ DOM 内容加载时间较长，建议优化')
      }
    }
  }
})
