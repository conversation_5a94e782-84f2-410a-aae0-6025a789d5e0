import { ElMessage } from 'element-plus'

const HTTP_STATUS_MESSAGES = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求的资源不存在',
  405: '请求方法不允许',
  408: '请求超时',
  500: '服务器内部错误',
  501: '服务未实现',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  505: 'HTTP版本不受支持'
}
/**
 * 初始化store状态
 * @param {Object} store - PromiseState实例
 * @param {string} path - API路径
 * @param {Object} data - 请求数据
 * @returns {Object} 更新后的store
 */
export const initializeStore = (store, path, data) => {
  store.b = new Date()
  store.t = 'ajax'
  store.u = path
  store.r = data
  store.h = {}
  store.p = true
  store.o = false
  store.e = false
  store.m = ''
  store.d = {}
  store.s += 1
  return store
}

/**
 * 处理请求成功响应
 * @param {Object} store - PromiseState实例
 * @param {Object} res - 响应对象
 * @param {boolean} isFormData - 是否为表单数据
 * @param {boolean} showSuccessMessage - 是否显示成功提示
 * @returns {boolean} 是否处理成功
 */
export const handleSuccess = (
  store,
  res,
  isFormData,
  showSuccessMessage = false,
  showErrorMessage = true
) => {
  store.f = new Date()
  store.p = false
  store.c = res.data?.resultCode

  if (isFormData) {
    store.d = res.data
  } else {
    store.d = res.data?.resData !== undefined ? res.data.resData : res.data
  }

  if (store.c === '0000') {
    store.o = true
    store.e = false
    store.m = res.data?.resultDesc ?? '服务调用成功'
    store.h = res.headers
    if (showSuccessMessage) {
      ElMessage({
        message: store.m,
        type: 'success',
        duration: 1000
      })
    }
    return true
  } else {
    store.o = false
    store.e = true
    store.h = res.headers
    store.m = res.data?.resultDesc || '业务处理失败'
    if (showErrorMessage) {
      ElMessage({
        message: store.m,
        type: 'error',
        duration: 1000
      })
    }
    return false
  }
}

/**
 * 处理请求错误（接口报错：HTTP状态码不是200）
 * @param {Object} store - PromiseState实例
 * @param {Error} err - 错误对象
 * @param {boolean} showErrorMessage - 是否显示错误提示
 */
export const handleError = (store, err) => {
  store.f = new Date()
  store.p = false
  store.o = false
  store.e = true
  store.c = err.response?.data?.resultCode || '9999'

  const status = err.response?.status
  const errorMessage = err.response?.data?.resultDesc

  if (status && HTTP_STATUS_MESSAGES[status]) {
    store.m = HTTP_STATUS_MESSAGES[status]
  } else if (errorMessage) {
    store.m = errorMessage
  } else {
    store.m = '网络异常，请稍后重试'
  }

  ElMessage({
    message: store.m,
    type: 'error'
  })
  store.d = err.response?.data?.resData || null
  throw new Error(store.m)
}
