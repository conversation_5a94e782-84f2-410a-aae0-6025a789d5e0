// import { encrypt, decrypt } from '@/utils/aesUtil'
import { blobToJson } from './fileHandler.js'
import { cancelControl } from './cancelControl'
import { useAuthStore } from '@/stores/authStore'
// const isDev = import.meta.env.MODE === 'development'

const isFileHeader = (contentType) => {
  return (
    contentType?.includes('application/octet-stream') ||
      contentType?.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') ||
      contentType?.includes('application/vnd.ms-excel'),
    contentType?.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
  )
}

/**
 * 创建全局HTTP拦截器
 * @param {Object} axiosInstance - Axios实例
 */
export const setupInterceptors = (axiosInstance) => {
  axiosInstance.interceptors.request.use(
    (config) => {
      if (config.cancelable) {
        const apiKey = `${config.method}-${config.url}`
        config.signal = cancelControl.setAbortAPI(apiKey)
      }

      try {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authentication = authStore.token
        }
      } catch (error) {
        console.warn('无法获取认证token:', error)
      }

      // 处理FormData类型请求
      if (
        config.data instanceof FormData ||
        (config.headers && config.headers['Content-Type'] === 'multipart/form-data')
      ) {
        return config
      }

      if (config.method === 'get' && config.params) {
        // config.params = isDev ? config.params : { encrypt: encrypt(config.params) }
      }

      if (config.method === 'post' && config.data) {
        // config.data = isDev ? config.data : { encrypt: encrypt(config.data) }
      }

      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  axiosInstance.interceptors.response.use(
    async (response) => {
      console.log('response=====', response)
      if (response.data instanceof Blob && response.data.type !== 'application/json') {
        return response
      }
      if (isFileHeader(response.headers['content-type'])) {
        return response
      }
      if (response.data instanceof Blob && response.data.type === 'application/json') {
        response.data = await blobToJson(response.data)
      }

      // if (response.data && typeof response.data === 'object' && response.data.encrypt) {
      //   try {
      //     response.data = isDev ? response.data.encrypt : decrypt(response.data.encrypt)
      //   } catch (error) {
      //     console.error('响应数据解密失败:', error)
      //   }
      // }

      return response
    },
    (error) => {
      if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
        console.log('Request canceled:', error.message)
        return Promise.reject({
          code: 'REQUEST_CANCELED',
          message: '请求已取消'
        })
      }
      console.error('API请求错误:', error)
      return Promise.reject(error)
    }
  )
}
