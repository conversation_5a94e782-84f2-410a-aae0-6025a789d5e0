// Element Plus 主题配置

// 主色调
$primary-color: #ea6078;
$primary-light-1: #ec7085;
$primary-light-2: #ee8092;
$primary-light-3: #f0909f;
$primary-light-4: #f2a0ac;
$primary-light-5: #f4b0b9;
$primary-light-6: #f6c0c6;
$primary-light-7: #f8d0d3;
$primary-light-8: #fae0e0;
$primary-light-9: #fcf0ed;
$primary-dark-1: #d2566c;
$primary-dark-2: #ba4c60;

// 成功色
// $success-color: #67c23a;
// $success-light-1: #73c947;
// $success-light-2: #7fd054;
// $success-light-3: #8bd761;
// $success-light-4: #97de6e;
// $success-light-5: #a3e57b;
// $success-light-6: #afec88;
// $success-light-7: #bbf395;
// $success-light-8: #c7faa2;
// $success-light-9: #d3ffaf;
// $success-dark-1: #5daf34;
// $success-dark-2: #539c2e;

// 警告色
// $warning-color: #e6a23c;
// $warning-light-1: #e8ab49;
// $warning-light-2: #eab456;
// $warning-light-3: #ecbd63;
// $warning-light-4: #eec670;
// $warning-light-5: #f0cf7d;
// $warning-light-6: #f2d88a;
// $warning-light-7: #f4e197;
// $warning-light-8: #f6eaa4;
// $warning-light-9: #f8f3b1;
// $warning-dark-1: #cf9236;
// $warning-dark-2: #b88230;

// 危险色
// $danger-color: #f56c6c;
// $danger-light-1: #f67979;
// $danger-light-2: #f78686;
// $danger-light-3: #f89393;
// $danger-light-4: #f9a0a0;
// $danger-light-5: #faadad;
// $danger-light-6: #fbbaba;
// $danger-light-7: #fcc7c7;
// $danger-light-8: #fdd4d4;
// $danger-light-9: #fee1e1;
// $danger-dark-1: #dd6161;
// $danger-dark-2: #c55656;

// // 信息色
// $info-color: #909399;
// $info-light-1: #9ca0a6;
// $info-light-2: #a8adb3;
// $info-light-3: #b4bac0;
// $info-light-4: #c0c7cd;
// $info-light-5: #ccd4da;
// $info-light-6: #d8e1e7;
// $info-light-7: #e4eef4;
// $info-light-8: #f0fbff;
// $info-light-9: #fcffff;
// $info-dark-1: #82868a;
// $info-dark-2: #74797b;

:root {
  // 主色调
  --el-color-primary: #{$primary-color};
  --el-color-primary-light-1: #{$primary-light-1};
  --el-color-primary-light-2: #{$primary-light-2};
  --el-color-primary-light-3: #{$primary-light-3};
  --el-color-primary-light-4: #{$primary-light-4};
  --el-color-primary-light-5: #{$primary-light-5};
  --el-color-primary-light-6: #{$primary-light-6};
  --el-color-primary-light-7: #{$primary-light-7};
  --el-color-primary-light-8: #{$primary-light-8};
  --el-color-primary-light-9: #{$primary-light-9};
  --el-color-primary-dark-1: #{$primary-dark-1};
  --el-color-primary-dark-2: #{$primary-dark-2};

  // 成功色
  // --el-color-success: #{$success-color};
  // --el-color-success-light-1: #{$success-light-1};
  // --el-color-success-light-2: #{$success-light-2};
  // --el-color-success-light-3: #{$success-light-3};
  // --el-color-success-light-4: #{$success-light-4};
  // --el-color-success-light-5: #{$success-light-5};
  // --el-color-success-light-6: #{$success-light-6};
  // --el-color-success-light-7: #{$success-light-7};
  // --el-color-success-light-8: #{$success-light-8};
  // --el-color-success-light-9: #{$success-light-9};
  // --el-color-success-dark-1: #{$success-dark-1};
  // --el-color-success-dark-2: #{$success-dark-2};

  // 警告色
  // --el-color-warning: #{$warning-color};
  // --el-color-warning-light-1: #{$warning-light-1};
  // --el-color-warning-light-2: #{$warning-light-2};
  // --el-color-warning-light-3: #{$warning-light-3};
  // --el-color-warning-light-4: #{$warning-light-4};
  // --el-color-warning-light-5: #{$warning-light-5};
  // --el-color-warning-light-6: #{$warning-light-6};
  // --el-color-warning-light-7: #{$warning-light-7};
  // --el-color-warning-light-8: #{$warning-light-8};
  // --el-color-warning-light-9: #{$warning-light-9};
  // --el-color-warning-dark-1: #{$warning-dark-1};
  // --el-color-warning-dark-2: #{$warning-dark-2};

  // 危险色
  // --el-color-danger: #{$danger-color};
  // --el-color-danger-light-1: #{$danger-light-1};
  // --el-color-danger-light-2: #{$danger-light-2};
  // --el-color-danger-light-3: #{$danger-light-3};
  // --el-color-danger-light-4: #{$danger-light-4};
  // --el-color-danger-light-5: #{$danger-light-5};
  // --el-color-danger-light-6: #{$danger-light-6};
  // --el-color-danger-light-7: #{$danger-light-7};
  // --el-color-danger-light-8: #{$danger-light-8};
  // --el-color-danger-light-9: #{$danger-light-9};
  // --el-color-danger-dark-1: #{$danger-dark-1};
  // --el-color-danger-dark-2: #{$danger-dark-2};

  // // 信息色
  // --el-color-info: #{$info-color};
  // --el-color-info-light-1: #{$info-light-1};
  // --el-color-info-light-2: #{$info-light-2};
  // --el-color-info-light-3: #{$info-light-3};
  // --el-color-info-light-4: #{$info-light-4};
  // --el-color-info-light-5: #{$info-light-5};
  // --el-color-info-light-6: #{$info-light-6};
  // --el-color-info-light-7: #{$info-light-7};
  // --el-color-info-light-8: #{$info-light-8};
  // --el-color-info-light-9: #{$info-light-9};
  // --el-color-info-dark-1: #{$info-dark-1};
  // --el-color-info-dark-2: #{$info-dark-2};
}

// 特殊组件样式覆盖
.el-button--primary {
  background: linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%);
  border: none !important;

  &:hover,
  &:focus {
    background: linear-gradient(101deg, #ec7085 0%, #c087a7 64%, #a6d0f9 100%);
    border: none !important;
    box-shadow: 0 4px 12px rgb(234 96 120 / 25%);
  }

  &:active {
    background: linear-gradient(101deg, #d2566c 0%, #a8739a 64%, #8fc4f7 100%);
    border: none !important;
  }
}

.el-button {
  &.el-button--primary {
    border: none !important;

    &:hover,
    &:focus,
    &:active {
      border: none !important;
    }
  }

  // 默认按钮（如取消按钮）添加淡淡的边框
  &:not(.el-button--primary) {
    border: 1px solid #e4e7ed !important;

    &:hover {
      border-color: #c0c4cc !important;
    }

    &:focus,
    &:active {
      border-color: #a0a4aa !important;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: #ea6078;
  border-color: #ea6078;
}

.el-radio__input.is-checked + .el-radio__label {
  color: #ea6078;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #ea6078;
  border-color: #ea6078;
}

.el-input__wrapper.is-focus {
  border-color: #ea6078;
  box-shadow: 0 0 0 2px rgb(234 96 120 / 20%);
}

.el-select .el-input.is-focus .el-input__wrapper {
  border-color: #ea6078;
  box-shadow: 0 0 0 2px rgb(234 96 120 / 20%);
}

.el-date-editor.el-input.is-focus .el-input__wrapper {
  border-color: #ea6078;
  box-shadow: 0 0 0 2px rgb(234 96 120 / 20%);
}

// 上传组件样式
.el-upload-dragger:hover {
  border-color: #ea6078;
}

.el-upload-dragger.is-dragover {
  border-color: #ea6078;
}

// 对话框样式
.el-dialog__header {
  border-bottom: 1px solid #f0f2f5;

  .el-dialog__title {
    font-weight: 600;
    color: #ea6078;
  }
}

// 消息框样式
.el-message-box {
  .el-message-box__title {
    font-weight: 600;
    color: #ea6078;
  }

  .el-message-box__header {
    .el-message-box__title {
      color: #ea6078;
    }
  }
}

// 消息提示样式
// .el-message--success .el-message__icon {
//   color: $success-color;
// }

// .el-message--warning .el-message__icon {
//   color: $warning-color;
// }

// .el-message--error .el-message__icon {
//   color: $danger-color;
// }

// .el-message--info .el-message__icon {
//   color: $info-color;
// }
