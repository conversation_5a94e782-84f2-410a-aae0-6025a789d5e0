import { renderAsync } from 'docx-preview'
import { downloadTaskReport } from '@/api'

export function useDocumentPreview() {
  const drawerVisible = ref(false)
  const previewLoading = ref(false)
  const previewError = ref('')
  const previewContainer = useTemplateRef('previewContainer')
  const documentLoaded = ref(false)

  // 下载按钮状态
  const downloadButtonText = computed(() => {
    if (previewLoading.value) return '加载中...'
    if (previewError.value) return '下载文档'
    if (!documentLoaded.value) return '下载文档'
    return '下载文档'
  })

  const canDownload = computed(() => {
    return !previewLoading.value
  })

  // 获取文档流用于预览
  const getDocumentStream = async (taskId) => {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/task/downloadReport`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: localStorage.getItem('token') || ''
      },
      body: JSON.stringify({ taskId })
    })

    if (!response.ok) {
      throw new Error(`获取文档失败: ${response.status} ${response.statusText}`)
    }

    return response.arrayBuffer()
  }

  // 预览报告
  const handlePreviewReport = () => {
    drawerVisible.value = true
  }

  const handleDownloadReport = async (task) => {
    try {
      await downloadTaskReport(task.taskId)
    } catch (error) {
      ElMessage.error(error.message || '下载失败')
    }
  }

  // 加载文档预览
  const loadDocumentPreview = async (task) => {
    console.log('开始加载文档预览...')
    previewLoading.value = true
    previewError.value = ''
    documentLoaded.value = false

    try {
      await nextTick()
      console.log('检查预览容器:', previewContainer.value)

      if (!previewContainer.value) {
        throw new Error('预览容器未找到，请稍后重试')
      }

      previewContainer.value.innerHTML = ''
      console.log('开始获取文档...')

      // 调用后台接口获取文档流
      const arrayBuffer = await getDocumentStream(task.taskId)

      console.log('文档获取成功，开始渲染...')

      if (!previewContainer.value) {
        throw new Error('预览容器在渲染前丢失')
      }

      await renderAsync(arrayBuffer, previewContainer.value, null, {
        className: 'docx-preview',
        inWrapper: false,
        ignoreWidth: false,
        ignoreHeight: true,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        useMathMLPolyfill: false,
        renderChanges: false,
        renderComments: false,
        renderEndnotes: true,
        renderFootnotes: true,
        renderHeaders: true,
        renderFooters: true
      })

      console.log('文档渲染完成')
      documentLoaded.value = true
    } catch (error) {
      console.error('预览文档失败:', error)
      previewError.value = error.message || '文档预览失败，请稍后重试'
      documentLoaded.value = false
    } finally {
      previewLoading.value = false
    }
  }

  // 重试预览
  const retryPreview = (task) => {
    loadDocumentPreview(task)
  }

  // 关闭抽屉
  const handleDrawerClose = () => {
    drawerVisible.value = false
    previewLoading.value = false
    previewError.value = ''
    documentLoaded.value = false

    if (previewContainer.value) {
      previewContainer.value.innerHTML = ''
    }
  }

  return {
    // 状态
    drawerVisible,
    previewLoading,
    previewError,
    previewContainer,
    documentLoaded,

    // 计算属性
    downloadButtonText,
    canDownload,

    // 方法
    handlePreviewReport,
    handleDownloadReport,
    loadDocumentPreview,
    retryPreview,
    handleDrawerClose
  }
}
