import { spost } from '@/request'

/**
 * 用户登录接口
 * @param {Object} loginData - 登录数据
 * @param {string} loginData.username - 用户名
 * @param {string} loginData.password - 密码
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const login = async (loginData, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(store, '/user/login', loginData, {
    showSuccessMessage: true,
    showErrorMessage: true,
    ...restOptions
  })
}

/**
 * 用户登出接口
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const logout = async (options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(
    store,
    '/user/logout',
    {},
    {
      showSuccessMessage: true,
      showErrorMessage: true,
      ...restOptions
    }
  )
}
