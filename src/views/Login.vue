<template>
  <div class="login-page">
    <div class="login-container">
      <div ref="headerRef" class="form-header">
        <img
          src="@/assets/img/robot-title.svg"
          alt="robot"
          class="robot-avatar"
          width="665"
          height="170"
        />
      </div>
      <div ref="formRef" class="form-content">
        <el-form
          ref="elFormRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="centered-form"
        >
          <el-form-item label="账号：" prop="username">
            <el-input v-model="form.username" placeholder="请输入账号" @keyup.enter="handleLogin" />
          </el-form-item>
          <el-form-item label="密码：" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item label="验证码：" prop="verifyCode">
            <div class="verify-code-wrapper">
              <el-input
                v-model="form.verifyCode"
                placeholder="请输入验证码"
                maxlength="6"
                @keyup.enter="handleLogin"
              />
              <button
                class="send-code-btn"
                :disabled="countdown > 0 || sendingCode"
                type="button"
                @click="handleSendCode"
              >
                <el-icon v-if="sendingCode" class="is-loading">
                  <i-svg-spinners-3-dots-scale />
                </el-icon>
                <span v-if="sendingCode">发送中...</span>
                <span v-else-if="countdown > 0">{{ countdown }}s后重发</span>
                <span v-else>发送验证码</span>
              </button>
            </div>
          </el-form-item>
          <el-form-item class="button-form-item">
            <div class="action-btn-wrapper">
              <button
                class="login-btn"
                :disabled="loginStore.p"
                type="button"
                :style="{
                  padding: loginStore.p ? '8px 16px' : '8px 45px'
                }"
                @click="handleLogin"
              >
                <el-icon v-if="loginStore.p" class="is-loading">
                  <i-svg-spinners-3-dots-scale />
                </el-icon>
                {{ loginStore.p ? '登录中...' : '登录' }}
              </button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@stores/authStore'
import { usePageAnimation } from '@hooks'
import { login as loginAPI } from '@api'
import { newStore } from '@request'
import md5 from 'md5'

const router = useRouter()
const authStore = useAuthStore()
const { initStandardPageAnimation } = usePageAnimation()

const elFormRef = useTemplateRef('elFormRef')
const headerRef = useTemplateRef('headerRef')
const formRef = useTemplateRef('formRef')

const form = reactive({
  username: '',
  password: '',
  verifyCode: ''
})

const loginStore = newStore()
const countdown = ref(0)
const sendingCode = ref(false)
let countdownTimer = null

const rules = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}

const handleSendCode = async () => {
  if (!form.username.trim()) {
    ElMessage.warning('请先输入账号')
    return
  }

  try {
    sendingCode.value = true

    await new Promise((resolve) => setTimeout(resolve, 800))

    ElMessage.success('验证码已发送')

    sendingCode.value = false
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }, 1000)
  } catch (error) {
    sendingCode.value = false
    ElMessage.error(error)
  }
}

const handleLogin = async () => {
  const valid = await elFormRef.value.validate()
  if (!valid) {
    ElMessage.error('请完善登录信息')
    return
  }

  try {
    const res = await loginAPI(
      {
        username: form.username,
        password: md5(form.password),
        authenticode: form.verifyCode || '8346'
      },
      { store: loginStore }
    )

    if (res.o && res.d) {
      authStore.login({
        token: res.d.token,
        userInfo: res.d.userInfo
      })

      await router.push('/Dashboard')
    }
  } catch (error) {
    ElMessage.error(error)
  }
}

onMounted(() => {
  if (authStore.isLoggedIn) {
    router.push('/Dashboard')
    return
  }

  initStandardPageAnimation(headerRef, formRef)
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  @include flex(column, center, flex-start);

  width: 100%;
  height: 100vh;
  padding-top: 10vh;
  background: url('@/assets/img/background.svg') center/cover;

  .login-container {
    @include flex(column, center, center);

    .form-header {
      @include flex-center;

      opacity: 0;
      transform: translateY(30px);

      .robot-avatar {
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }

    .form-content {
      @include flex-center;

      width: 100%;
      padding: 40px 64px 28px 30px;
      background: #fff;
      border-radius: 30px;
      box-shadow: 0 4px 12px 4px rgb(128 128 128 / 15%);
      opacity: 0;
      transform: translateY(30px);

      .centered-form {
        @include flex(column, center, flex-start, 15px);

        width: 100%;

        :deep(.el-form-item) {
          @include flex(row, center);

          .el-form-item__label {
            padding-right: 20px;
            font-size: 16px;
            font-weight: 400;
            color: #000;
            text-align: right;
          }

          .el-form-item__content {
            .el-input {
              width: 100%;

              .el-input__wrapper {
                @include input-wrapper(400px, 42px, #f6f6f6, transparent);

                outline: none !important;
                border: none !important;
                box-shadow: none !important;

                &:hover {
                  border: none !important;
                  box-shadow: none !important;
                }

                &.is-focus {
                  border: none !important;
                  box-shadow: none !important;
                }
              }

              .el-input__inner {
                @include input-inner(0 16px, 14px, #000);

                line-height: 24px;

                &:focus {
                  outline: none !important;
                  border: none !important;
                  box-shadow: none !important;
                }

                &::placeholder {
                  color: #94a0ad;
                }
              }
            }

            .verify-code-wrapper {
              @include flex(row, center, flex-start, 12px);

              width: 400px;

              .el-input {
                flex: 1;

                .el-input__wrapper {
                  @include input-wrapper(100%, 42px, #f6f6f6, transparent);
                }
              }

              .send-code-btn {
                @include flex-center;

                flex-shrink: 0;
                gap: 6px;
                width: 120px;
                height: 42px;
                font-size: 14px;
                font-weight: 500;
                color: #fff;
                cursor: pointer;
                border: none;
                border-radius: 8px;
                transition: all 0.3s ease;

                // 默认状态：主题渐变色
                &:not(:disabled) {
                  background: linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%);

                  &:hover {
                    background: linear-gradient(101deg, #ec7085 0%, #c087a7 64%, #a6d0f9 100%);
                    box-shadow: 0 4px 12px rgb(234 96 120 / 25%);
                  }

                  &:active {
                    background: linear-gradient(101deg, #d2566c 0%, #a8739a 64%, #8fc4f7 100%);
                  }
                }

                // 禁用状态：灰色
                &:disabled {
                  color: #fff;
                  cursor: not-allowed;
                  background: #c0c4cc;
                  box-shadow: none;
                }

                .el-icon {
                  font-size: 14px;
                  @include rotating-animation;
                }
              }
            }
          }

          &:last-child {
            margin-bottom: 0;
          }

          &.button-form-item {
            .el-form-item__content {
              margin-left: 0 !important;
            }
          }
        }
      }
    }
  }

  .action-btn-wrapper {
    @include flex(row, center, center);

    .login-btn {
      @include gradient-button(linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%));
      @include flex-start-center(8px);

      margin-top: 15px;
      margin-left: 30px;
      font-size: 16px;
      color: #fff;
      border: unset;
      border-radius: 8px;

      .el-icon {
        font-size: 16px;
        @include rotating-animation;
      }
    }
  }
}
</style>
