<template>
  <div class="dashboard">
    <aside ref="sidebarRef" class="sidebar">
      <header ref="logoRef" class="logo-section">
        <img src="@/assets/img/logo.svg" alt="logo" class="logo-main" />
      </header>

      <el-button ref="newTaskBtnRef" class="new-task-btn" @click="handleNewTask">
        <i-ep-plus />新建任务
      </el-button>
      <TaskSearch
        ref="taskSearchRef"
        v-model="form"
        @search="handleSearch"
        @reset="handleSearchReset"
        @refresh="handleRefresh"
      />
      <TaskList
        ref="taskListRef"
        :tasks="taskStore.tasks"
        :loading="taskListStore.p"
        @refresh="handleRefresh"
        @delete="handleDeleteTask"
      />
    </aside>

    <main class="content-area">
      <!-- 用户信息区域 -->
      <div ref="userInfoRef" class="user-info-area">
        <UserDropdown />
      </div>

      <!-- 主内容 -->
      <div class="main-content">
        <Transition name="content-fade" mode="out-in">
          <TaskForm v-if="taskStore.isCreatingNewTask" key="task-form" />
          <TaskExecution
            v-else-if="taskStore.currentTask"
            key="task-execution"
            :task="taskStore.currentTask"
            :show-decimal-time="false"
          />
        </Transition>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useTaskStore } from '@stores/taskStore'
import { useDashboardAnimation } from '@hooks/useDashboardAnimation'
import TaskSearch from '@components/TaskSearch.vue'
import TaskList from '@components/TaskList.vue'
import TaskForm from '@components/TaskForm.vue'
import TaskExecution from '@components/TaskExecution/index.vue'
import UserDropdown from '@components/UserDropdown.vue'
import { getTaskList, deleteTask as deleteTaskApi } from '@api'
import { newStore } from '@request'

const taskStore = useTaskStore()
const { initStandardDashboardAnimation } = useDashboardAnimation()

// 模板引用
const sidebarRef = useTemplateRef('sidebarRef')
const logoRef = useTemplateRef('logoRef')
const newTaskBtnRef = useTemplateRef('newTaskBtnRef')
const taskSearchRef = useTemplateRef('taskSearchRef')
const taskListRef = useTemplateRef('taskListRef')
const userInfoRef = useTemplateRef('userInfoRef')

const form = ref({
  sysName: '',
  sysUrl: '',
  createDate: ''
})

onMounted(async () => {
  try {
    console.log('=== Dashboard初始化开始 ===')

    console.log('获取最新任务列表...')
    handleSearch(form.value)
    console.log('Dashboard初始化完成 - 已清除缓存，回到新建任务状态')
    // 初始化页面动画
  } catch (error) {
    console.error('初始化任务列表失败:', error)
  } finally {
    taskStore.startNewTask()
    initStandardDashboardAnimation({
      sidebarRef,
      logoRef,
      newTaskBtnRef,
      taskSearchRef,
      taskListRef,
      userInfoRef
    })
  }
})

const handleNewTask = () => {
  taskStore.startNewTask()
}

const handleRefresh = async () => {
  await handleSearch(form.value)
  console.log('Dashboard: 任务列表刷新完成')
}
const taskListStore = newStore()
const handleSearch = async (params) => {
  try {
    const result = await getTaskList(params, { store: taskListStore })
    if (result.e) throw new Error(result.m)
    taskStore.updateTaskList(result.d || [])
  } catch (error) {
    taskStore.updateTaskList([])
    console.log(error)
  }
}

const handleSearchReset = async () => {
  form.value = {
    sysName: '',
    sysUrl: '',
    createDate: ''
  }

  taskStore.startNewTask()
  await handleSearch(form.value)
}

// 删除任务
const handleDeleteTask = async (task) => {
  await ElMessageBox.confirm(
    `确定要删除任务"${task.sysName}渗透复测报告"吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: false
    }
  )

  try {
    const res = await deleteTaskApi(task.taskId)
    if (res.e) return
    const result = taskStore.deleteTask(task.taskId)
    if (result.success) {
      if (result.wasCurrentTask || taskStore.tasks.length === 0) {
        taskStore.startNewTask()
        console.log('Dashboard: 已切换到新建任务状态')
      }
    }
  } catch (e) {
    ElMessage.error(e)
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  @include flex(row, center, center, 5px);

  width: 100%;
  height: 100vh;
  padding: 0 30px 36px 10px;
  overflow: hidden;
  background: url('@/assets/img/background.svg') center/cover;

  .sidebar {
    @include flex(column, center, flex-start, 15px);

    flex-shrink: 0;
    max-width: 380px;
    height: 100%;
    padding-top: 25px;
    opacity: 0;
    transform: translateX(-50px) scale(0.95);

    .logo-section {
      width: 292px;
      height: 66px;
      opacity: 0;
      transform: translateY(-20px) scale(0.8);

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .new-task-btn {
      width: 80%;
      padding: 18px 0;
      font-size: 15px;
      font-weight: 500;
      color: #2d404f;
      background: #e5edfb;
      border-radius: 7px;
      opacity: 0;
      transform: translateY(0) scale(0.8);
    }
  }

  .content-area {
    @include flex(column, stretch, flex-start);

    position: relative;
    flex: 1;
    height: 100%;

    .user-info-area {
      position: absolute;
      top: 10px;
      right: 0;
      z-index: 10;
      opacity: 0;
      transform: translate(30px, -10px);
    }

    .main-content {
      @include flex-center;

      flex: 1;
      height: 100%;

      .content-fade {
        @include fade-transition;
      }
    }
  }
}
</style>
