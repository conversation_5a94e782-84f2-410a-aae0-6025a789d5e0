<template>
  <div class="task-form">
    <div ref="headerRef" class="form-header">
      <img src="@/assets/img/robot-title.svg" alt="robot" class="robot-avatar" />
    </div>
    <div ref="formRef" class="form-content">
      <el-form
        ref="elFormRef"
        :model="form"
        :rules="rules"
        label-width="200px"
        class="centered-form"
      >
        <el-form-item label="目标系统名称：" prop="sysName">
          <el-input v-model="form.sysName" placeholder="请输入目标系统名称" />
        </el-form-item>
        <el-form-item label="目标系统URL：" prop="sysUrl">
          <el-input v-model="form.sysUrl" placeholder="请输入目标系统URL" />
        </el-form-item>
        <el-form-item label="是否预设漏洞：" prop="presetVulnerability">
          <el-radio-group v-model="form.presetVulnerability">
            <el-radio :value="true">是</el-radio>
            <el-radio :value="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="">
          <div class="action-btn-wrapper">
            <button
              class="start-btn"
              :disabled="taskStore.taskLoading"
              type="button"
              @click="onSubmit"
            >
              <el-icon v-if="taskStore.taskLoading" class="is-loading">
                <i-svg-spinners-3-dots-scale />
              </el-icon>
              <el-icon v-else> <i-tabler:location /></el-icon>
              {{ taskStore.taskLoading ? '正在创建任务...' : '开始执行' }}
            </button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 文件上传弹窗 -->
    <FileUploadDialog
      v-model="showUploadDialog"
      @upload-success="handleUploadSuccess"
      @cancel="handleUploadCancel"
    />
  </div>
</template>

<script setup>
import { useTaskStore } from '@stores/taskStore'
import { usePageAnimation } from '@hooks'
import FileUploadDialog from '@/components/common/FileUploadDialog.vue'

const taskStore = useTaskStore()
const { initStandardPageAnimation } = usePageAnimation()

const elFormRef = useTemplateRef('elFormRef')
const headerRef = useTemplateRef('headerRef')
const formRef = useTemplateRef('formRef')

const form = reactive({
  sysName: '',
  sysUrl: '',
  presetVulnerability: true
})

const showUploadDialog = ref(false)
const uploadedFile = ref(null)

const validateUrl = (_rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入目标系统URL'))
    return
  }

  let hostPart = value.replace(/^https?:\/\//, '')
  hostPart = hostPart.split(/[:/]/)[0]

  // 检查是否为IP地址格式
  const isIpFormat = /^(\d{1,3}\.){3}\d{1,3}$/.test(hostPart)

  if (isIpFormat) {
    // IP地址校验：需要包含大于等于4个点（即至少5段）
    const ipParts = hostPart.split('.')
    if (ipParts.length < 5) {
      callback(new Error('IP地址格式不正确，请重新输入'))
      return
    }

    // 验证每段IP是否在有效范围内
    for (const part of ipParts) {
      const num = parseInt(part, 10)
      if (isNaN(num) || num < 0 || num > 255) {
        callback(new Error('IP地址格式不正确，请重新输入'))
        return
      }
    }
  } else {
    // 域名校验：需要包含大于等于2个点（即至少3段）
    const domainParts = hostPart.split('.')
    if (domainParts.length < 3) {
      callback(new Error('域名格式不正确，请重新输入'))
      return
    }

    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/
    for (const part of domainParts) {
      if (!part || !domainRegex.test(part)) {
        callback(new Error('域名格式不正确'))
        return
      }
    }
  }

  callback()
}

const rules = {
  sysName: [{ required: true, message: '请输入目标系统名称', trigger: 'blur' }],
  sysUrl: [
    { required: true, message: '请输入目标系统URL', trigger: 'blur' },
    { validator: validateUrl, trigger: ['blur', 'change'] }
  ]
}

const onSubmit = async () => {
  if (taskStore.taskLoading) return
  const valid = await elFormRef.value.validate()
  if (!valid) {
    ElMessage.error('请完善表单信息')
    return
  }

  // 如果选择了预设漏洞但还没有上传文件，弹出上传对话框
  if (form.presetVulnerability && !uploadedFile.value) {
    showUploadDialog.value = true
    return
  }

  // 直接创建任务
  await createTask()
}

// 统一的任务创建方法
const createTask = async () => {
  const taskData = {
    ...form
  }

  if (form.presetVulnerability && uploadedFile.value) {
    taskData.fileName = uploadedFile.value.fileName
    taskData.fileId = uploadedFile.value.fileId
    taskData.filePath = uploadedFile.value.filePath
  }

  console.log('提交任务数据:', taskData)
  const taskId = await taskStore.createTaskAsync(taskData)
  if (!taskId) {
    console.log('任务创建失败，保持在表单页面')
    return false
  }
  console.log('任务创建成功，taskId:', taskId)
  resetForm()
  return true
}

const resetForm = () => {
  form.sysName = ''
  form.sysUrl = ''
  uploadedFile.value = null
  elFormRef.value?.clearValidate()
}

const handleUploadSuccess = async (fileInfo) => {
  uploadedFile.value = fileInfo
  showUploadDialog.value = false

  ElMessage.success({
    message: '文件上传成功！正在开始执行任务...',
    duration: 800
  })

  await createTask()
}

const handleUploadCancel = () => {
  showUploadDialog.value = false
}

onMounted(() => {
  initStandardPageAnimation(headerRef, formRef, {
    header: {
      duration: 0.6,
      delay: 0.2
    },
    form: {
      duration: 0.6,
      delay: 0.4
    },
    overlap: -0.2
  })
})
</script>

<style lang="scss" scoped>
.task-form {
  @include flex(column, center, $gap: 20px);

  width: 80%;
  height: 84%;
  padding: 0 20px 20px;

  .form-header {
    @include flex-center;

    opacity: 0;
    transform: translateY(30px);

    .robot-avatar {
      width: auto;
      height: auto;
      object-fit: contain;
    }
  }

  .form-content {
    @include flex-center;

    padding: 40px 64px 28px 30px;
    background: #fff;
    border-radius: 30px;
    box-shadow: 0 2px 6px 2px #8080800d;
    opacity: 0;
    transform: translateY(-10px);

    .centered-form {
      @include flex(column, center, flex-start, 15px);

      width: 100%;

      :deep(.el-form-item) {
        @include flex(row, center);

        .el-form-item__label {
          padding-right: 20px;
          font-size: 16px;
          font-weight: 400;
          color: #000;
          text-align: right;
        }

        .el-form-item__content {
          .el-input {
            width: 100%;

            .el-input__wrapper {
              @include input-wrapper(500px, 42px, #f6f6f6, transparent);

              outline: none !important;
              border: none !important;
              box-shadow: none !important;

              &:hover {
                border: none !important;
                box-shadow: none !important;
              }

              &.is-focus {
                border: none !important;
                box-shadow: none !important;
              }
            }

            .el-input__inner {
              @include input-inner(0 16px, 14px, #000);

              line-height: 24px;

              &:focus {
                outline: none !important;
                border: none !important;
                box-shadow: none !important;
              }

              &::placeholder {
                color: #94a0ad;
              }
            }
          }

          .el-radio-group {
            @include flex(row, center, flex-start, 50px);

            width: 500px;
            height: 42px;
            padding: 0 16px;

            .el-radio {
              margin-right: 0;

              .el-radio__input {
                .el-radio__inner {
                  width: 18px;
                  height: 18px;
                  background: #fff;
                  border: 2px solid #dcdfe6;
                  border-radius: 50%;
                  transition: all 0.3s ease;

                  &::after {
                    width: 8px;
                    height: 8px;
                    background: linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%);
                    border-radius: 50%;
                    transition: all 0.3s ease;
                  }
                }

                &.is-checked {
                  .el-radio__inner {
                    background: #fff;
                    border-color: #ea6078;
                  }
                }
              }

              .el-radio__label {
                padding-left: 8px;
                font-size: 14px;
                line-height: 24px;
                color: #000;
              }

              &:hover {
                .el-radio__input .el-radio__inner {
                  border-color: #ea6078;
                }
              }
            }
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .error-message {
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .action-btn-wrapper {
    @include flex(row, center, flex-end);

    width: 500px;

    .start-btn {
      @include gradient-button(linear-gradient(101deg, #ea6078 0%, #be7aa0 64%, #9fcdf8 100%));
      @include flex-start-center(8px);

      padding: 12px 32px;
      font-size: 16px;
      color: #fff;
      border-radius: 8px 8px 20px;

      .el-icon {
        font-size: 16px;
        @include rotating-animation;
      }
    }
  }
}
</style>
