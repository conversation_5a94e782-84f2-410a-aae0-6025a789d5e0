<template>
  <div class="user-dropdown">
    <el-dropdown trigger="hover" popper-class="user-dropdown-popper" @command="handleCommand">
      <div class="user-info">
        <div class="avatar">
          <el-icon><i-ep-user /></el-icon>
        </div>
        <span class="username">{{ userInfo?.username }}</span>
        <el-icon class="dropdown-icon">
          <i-ep-arrow-down />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="logout">
            <el-icon><i-ep-switch-button /></el-icon>
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { useAuthStore } from '@stores/authStore'

const authStore = useAuthStore()
const router = useRouter()

const userInfo = computed(() => authStore.userInfo)

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false
      })

      const loadingMessage = ElMessage({
        message: '正在退出登录...',
        type: 'info',
        duration: 0
      })

      authStore.logout()
      loadingMessage.close()
      await router.push('/login')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-dropdown {
  outline: none !important;

  .user-info {
    @include flex-start-center(8px);

    padding: 4px 8px;
    cursor: pointer;
    outline: none !important;
    background: rgb(255 255 255 / 90%);
    border: 1px solid rgb(255 255 255 / 40%);
    border-radius: 20px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 6%);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;

    .avatar {
      width: 30px;
      height: 30px;
      background: linear-gradient(135deg, #ea6078, #be7aa0);
      border-radius: 50%;
      @include flex-center;

      box-shadow: 0 2px 8px rgb(234 96 120 / 25%);

      .el-icon {
        font-size: 16px;
        color: white;
      }
    }

    .username {
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
      letter-spacing: 0.2px;
    }

    .dropdown-icon {
      font-size: 11px;
      color: #ea6078;
      opacity: 0.8;
      transition: all 0.3s ease;
    }

    &:hover .dropdown-icon {
      opacity: 1;
      transform: rotate(180deg);
    }
  }
}

:deep(.el-dropdown) {
  outline: none !important;

  .el-dropdown-selfdefine {
    outline: none !important;
    border: none !important;

    &:focus {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
}

:deep(.el-dropdown-menu) {
  min-width: 150px;
  padding: 6px 0;
  background: rgb(255 255 255 / 95%);
  border: 1px solid rgb(234 96 120 / 10%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(234 96 120 / 12%);
  backdrop-filter: blur(20px);

  .el-dropdown-menu__item {
    @include flex-start-center(10px);

    padding: 12px 18px;
    margin: 2px 8px;
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    border-radius: 8px;
    transition: all 0.25s ease;

    .el-icon {
      font-size: 16px;
      color: #ea6078;
      opacity: 0.8;
    }
  }
}
</style>
