<template>
  <el-dialog
    v-model="visible"
    title="上传预设漏洞文件"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @close="handleClose"
  >
    <div class="upload-dialog-content">
      <div class="upload-description">
        <p>请上传预设漏洞文件，支持的文件格式：.txt, .csv, .xlsx, .xls, .doc, .docx</p>
        <p>文件大小不超过 10MB</p>
      </div>

      <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        class="upload-demo"
        drag
        :before-upload="beforeUpload"
        :on-change="handleFileChange"
        :on-remove="handleRemove"
        :auto-upload="false"
        :limit="1"
        :on-exceed="handleExceed"
        accept=".txt,.csv,.xlsx,.xls,.doc,.docx"
      >
        <div class="upload-content">
          <el-icon class="el-icon--upload">
            <i-ep:upload-filled />
          </el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
      </el-upload>

      <div class="upload-tip">只能上传 txt/csv/xlsx/xls/doc/docx文件，且不超过 10MB</div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="uploading"
          :disabled="!selectedFile && fileList.length === 0"
          @click="handleConfirm"
        >
          {{ uploading ? '上传中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'cancel'])

const uploadRef = ref()
const fileList = ref([])
const uploading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedFile = ref(null)

const beforeUpload = (file) => {
  const allowedExtensions = ['.txt', '.csv', '.xlsx', '.xls', '.doc', '.docx']

  const fileName = file.name.toLowerCase()
  const hasValidExtension = allowedExtensions.some((ext) => fileName.endsWith(ext))

  if (!hasValidExtension) {
    ElMessage.error('只能上传 txt、csv、xls、xlsx、doc、docx 格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  return true
}

const handleFileChange = (file, fileListParam) => {
  console.log('文件选择变化:', file, fileListParam)
  selectedFile.value = file
  if (file && (file.status === 'ready' || file.raw)) {
    fileList.value = fileListParam || [file]
  }
}

// 移除文件
const handleRemove = (file) => {
  console.log('移除文件:', file)
}

// 文件数量超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件，请先移除已选择的文件!')
}

// 确定按钮点击
const handleConfirm = async () => {
  if (!selectedFile.value && fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件!')
    return
  }

  uploading.value = true

  try {
    const file = selectedFile.value || fileList.value[0]
    const timestamp = Date.now()
    const fileName = `${timestamp}_${file.name}`
    const filePath = `/uploads/vulnerability/${fileName}`

    await new Promise((resolve) => setTimeout(resolve, 1500))

    console.log('文件已保存到服务器:', filePath)

    emit('upload-success', {
      fileName: file.name,
      fileId: timestamp.toString(),
      filePath: filePath
    })

    handleClose()
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试!')
  } finally {
    uploading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleClose = () => {
  fileList.value = []
  uploading.value = false
  visible.value = false
}
</script>

<style lang="scss" scoped>
.upload-dialog-content {
  padding: 20px 0;

  .upload-description {
    padding: 16px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-left: 4px solid #ea6078;
    border-radius: 8px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
      color: #606266;

      &:first-child {
        margin-bottom: 8px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  :deep(.upload-demo) {
    .el-upload {
      position: relative;
      width: 100%;
      overflow: hidden;
      cursor: pointer;
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      transition: all 0.3s;

      &:hover {
        border-color: #ea6078;
      }
    }

    .el-upload-dragger {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 200px;
      background-color: #fafafa;
      border: none;
      border-radius: 12px;

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;

        .el-icon--upload {
          margin-bottom: 16px;
          font-size: 48px;
          color: #c0c4cc;
        }

        .el-upload__text {
          font-size: 16px;
          line-height: 1.5;
          color: #606266;

          em {
            font-style: normal;
            font-weight: 500;
            color: #ea6078;
          }
        }
      }
    }
  }

  .upload-tip {
    padding: 8px 12px;
    margin-top: 12px;
    font-size: 12px;
    color: #909399;
    text-align: center;
    background-color: #f5f7fa;
    border-left: 3px solid #ea6078;
    border-radius: 6px;
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
