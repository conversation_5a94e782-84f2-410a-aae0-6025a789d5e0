<template>
  <div class="task-list-container">
    <!-- 标题区域 -->
    <div class="list-header">
      <!-- <i-ep-list class="header-icon" /> -->
      <h3 class="header-title">任务列表</h3>
      <span class="task-count"> &nbsp;( {{ taskStore.taskCount }} )</span>
      <!-- 刷新 -->
      <!-- <el-button class="refresh-btn" @click="handleRefresh">
        <el-icon> <i-ep-refresh /></el-icon>&nbsp;刷新
      </el-button> -->
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loadingTip">
      <i-svg-spinners-blocks-shuffle-3 class="loading-icon" />
      <span>数据加载中...</span>
    </div>

    <!-- 任务列表 -->
    <div v-else-if="!loading" class="task-list">
      <AnimatedList animation-name="task" wrapper-class="task-list-wrapper">
        <div
          v-for="task in tasks"
          :key="task.taskId"
          class="task-item"
          :class="{
            active: taskStore.currentTask && taskStore.currentTask.taskId === task.taskId
          }"
          @click.stop="handleTaskSelect(task)"
        >
          <!-- 左侧：任务信息 -->
          <div class="task-info">
            <div class="task-header">
              <div class="task-name">{{ task.sysName }}</div>
              <div class="task-status">
                <div class="status-dot" :class="`status-${getStatusClass(task.status)}`"></div>
                <span class="status-text">{{ getStatusText(task.status) }}</span>
              </div>
              <div class="task-time">
                <!-- <span>{{ task.departName }}</span> -->
                <!-- <span>{{ task.staffName }}</span> -->
                <span>{{ formatTaskTime(task.startDate) }}</span>
              </div>
            </div>
          </div>

          <!-- 右侧：删除按钮 -->
          <div class="task-actions" @click.stop>
            <div class="delete-btn" title="删除任务" @click.stop="handleDeleteTask(task)">
              <i-ep-delete />
            </div>
          </div>
        </div>

        <div v-if="tasks.length === 0" class="empty-state">
          <el-empty description="暂无任务" />
        </div>
      </AnimatedList>
    </div>
  </div>
</template>

<script setup>
import { useTaskStore } from '@stores/taskStore'
import { formatTaskTime } from '@utils/timeUtils'
import AnimatedList from './common/AnimatedList.vue'

defineProps({
  tasks: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['delete'])

const taskStore = useTaskStore()

const handleTaskSelect = async (task) => {
  if (taskStore.currentTask?.taskId === task.taskId) {
    return
  }
  const isRunningTask = task.status === '1'
  if (isRunningTask) {
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
  taskStore.selectTask(task, 'SWITCH_TASK')
}

const handleDeleteTask = (task) => {
  emit('delete', task)
}

const getStatusText = (status) => {
  const statusMap = {
    0: '未执行',
    1: '执行中',
    9: '已完成',
    '-1': '失败',
    '-2': '已终止'
  }
  return statusMap[status] || '未知'
}

const getStatusClass = (status) => {
  const classMap = {
    0: 'pending',
    1: 'running',
    9: 'completed',
    '-1': 'failed',
    '-2': 'terminated'
  }
  return classMap[status] || 'pending'
}
</script>

<style lang="scss" scoped>
.task-list-container {
  @include flex(column);

  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.list-header {
  @include flex-start-center(5px);

  flex-shrink: 0;
  padding: 5px 16px 8px;
  font-size: 18px;
  font-weight: 600;

  .header-title {
    margin: 0;
    font-size: 18px;
    color: #2c3e50;
  }

  .task-count {
    color: #e6627a;
  }

  // .refresh-btn {
  //   padding: 5px 10px;
  //   margin-left: auto;
  //   font-size: 14px;
  //   font-weight: 500;
  //   color: #3b44fa;
  //   background: #bcc5ff;
  //   border: none;
  //   border-radius: 10px;

  //   &:hover {
  //     background: #a8b3ff;
  //   }

  //   &:active {
  //     background: #9aa8ff;
  //   }
  // }
}

.task-list {
  @include flex(column, stretch, flex-start, 12px);

  flex: 1;
  padding-top: 5px;
  margin-right: 35px;
  margin-left: 12px;
  overflow-y: auto;
  border-top: none;
  border-radius: 0 0 16px 16px;
  backdrop-filter: blur(10px);

  .task-item {
    @include hover-card;
    @include flex-between(center);

    flex-shrink: 0;
    padding: 10px 15px;
    overflow: hidden;
    cursor: pointer;

    // &::before {
    //   position: absolute;
    //   inset: 0;
    //   content: '';
    //   background: linear-gradient(135deg, rgb(255 192 203 / 1.5%) 0%, rgb(255 218 224 / 3%) 100%);
    //   opacity: 0;
    //   transition: opacity 0.25s ease;
    // }

    &:hover {
      box-shadow:
        0 2px 8px rgb(0 0 0 / 5%),
        0 0 0 1px rgb(255 182 193 / 6%);
      transform: translateY(-1px);

      &::before {
        opacity: 1;
      }
    }

    &.active {
      background: #fff;
      border-color: #ff6b9d;
      box-shadow:
        0 6px 20px rgb(255 107 157 / 15%),
        0 0 0 2px rgb(255 107 157 / 10%),
        inset 0 1px 0 rgb(255 255 255 / 80%);
      transform: translateY(-2px);

      &::before {
        background: linear-gradient(135deg, rgb(255 107 157 / 8%) 0%, rgb(255 182 193 / 12%) 100%);
        opacity: 1;
      }

      &::after {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 60%;
        content: '';
        background: linear-gradient(to bottom, #ff6b9d 0%, #ff8fab 50%, #ffb3c1 100%);
        border-radius: 0 4px 4px 0;
        box-shadow: 0 0 8px rgb(255 107 157 / 30%);
        transform: translateY(-50%);
      }

      .task-name {
        font-weight: 700;
        color: #1e293b;
      }

      .status-text {
        font-weight: 600;
        color: #ff6b9d;
      }
    }

    .task-actions {
      position: relative;
      z-index: 10;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      margin-left: 12px;

      .delete-btn {
        position: relative;
        z-index: 11;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        font-size: 14px;
        color: #f56c6c;
        cursor: pointer;
        background: rgb(245 108 108 / 10%);
        border-radius: 6px;
        transition: all 0.25s ease;

        &:hover {
          color: white;
          background: #f56c6c;
          transform: scale(1.05);
        }
      }
    }

    .task-info {
      position: relative;
      z-index: 1;
      flex: 1;
      min-width: 0;

      .task-header {
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: space-between;

        .task-name {
          flex: 1;
          margin-right: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.4;
          color: #2c3e50;
          white-space: nowrap;
        }

        .task-status {
          @include flex-start-center(6px);

          flex-shrink: 0;

          .status-dot {
            @include status-dot;
          }

          .status-text {
            font-size: 14px;
            font-weight: 500;
            color: #999;
          }
        }

        .task-time {
          @include flex-start-center(6px);

          font-size: 12px;
          font-weight: 400;
          color: #999;
          opacity: 0.8;
          transition: all 0.25s ease;
        }
      }
    }

    &.active .task-time {
      color: #6b7280;
      opacity: 0.9;
    }
  }

  .empty-state {
    text-align: center;
  }
}

.loadingTip {
  @include loading-container;

  flex: 1;
}
</style>
