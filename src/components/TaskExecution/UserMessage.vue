<template>
  <div ref="messageElement" class="message user-message">
    <div class="message-content">
      <div class="message-text">
        <span v-for="field in userFields" :key="field.key">
          {{ field.label }}：{{ field.value }}
        </span>
      </div>
      <div class="message-time">{{ getFriendlyTime(task.startDate) }}</div>
    </div>
  </div>
</template>

<script setup>
import { getFriendlyTime } from '@utils/timeUtils'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

const messageElement = ref(null)

const userFields = computed(() => [
  { key: 'sysName', label: '目标系统名称', value: props.task.sysName },
  { key: 'sysUrl', label: '目标系统URL', value: props.task.sysUrl },
  {
    key: 'presetVulnerability',
    label: '是否预设漏洞',
    value: props.task.presetVulnerability ? '是' : '否'
  },
  props.task.presetVulnerability
    ? { key: 'fileName', label: '预设漏洞文件名', value: props.task.fileName }
    : null
])

defineExpose({
  messageElement
})
</script>

<style lang="scss" scoped>
.message {
  @include message-base;

  &.user-message {
    @include user-message;
  }
}
</style>
