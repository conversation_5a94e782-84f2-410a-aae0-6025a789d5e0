<template>
  <el-drawer
    v-model="drawerVisible"
    :title="task.reportFileName"
    :size="'70%'"
    direction="rtl"
    :show-close="false"
    :before-close="handleDrawerClose"
  >
    <div class="document-preview-container">
      <div v-if="previewLoading" class="preview-loading">
        <el-icon class="is-loading">
          <i-svg-spinners-12-dots-scale-rotate />
        </el-icon>
        <span>正在加载文档...</span>
      </div>
      <div v-if="previewError" class="preview-error">
        <el-icon>
          <i-ep-warning-filled />
        </el-icon>
        <span>{{ previewError }}</span>
        <el-button type="primary" @click="retryPreview(task)">重试</el-button>
      </div>
      <div v-if="!previewLoading && !previewError && !documentLoaded" class="preview-empty">
        <el-icon>
          <i-ep-document />
        </el-icon>
        <span>点击预览按钮开始加载文档</span>
      </div>
      <div
        ref="previewContainer"
        class="preview-content"
        :style="{ display: previewLoading || previewError ? 'none' : 'block' }"
      ></div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleDrawerClose">关闭</el-button>
        <el-button
          type="primary"
          :disabled="!canDownload"
          :loading="previewLoading"
          @click="handleDownloadReport(task)"
        >
          {{ downloadButtonText }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { useDocumentPreview } from '@hooks'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

const {
  drawerVisible,
  previewLoading,
  previewError,
  previewContainer,
  documentLoaded,
  downloadButtonText,
  canDownload,
  handleDownloadReport,
  loadDocumentPreview,
  retryPreview,
  handleDrawerClose
} = useDocumentPreview()

defineExpose({
  drawerVisible,
  loadDocumentPreview
})

watch(drawerVisible, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      if (drawerVisible.value && previewContainer.value) {
        loadDocumentPreview(props.task)
      }
    }, 300)
  }
})
</script>

<style lang="scss" scoped>
.document-preview-container {
  display: flex;
  flex-direction: column;

  .preview-loading,
  .preview-error,
  .preview-empty {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;
    color: #909399;
    transform: translateY(100%);

    .el-icon {
      font-size: 48px;
    }

    span {
      font-size: 16px;
    }
  }

  .preview-error {
    color: #f56c6c;

    .el-icon {
      color: #f56c6c;
    }
  }

  .preview-content {
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    overflow: auto;
    background: #f5f5f5;

    :deep(.docx-preview) {
      box-sizing: border-box;
      width: 100% !important;
      font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 6px 0 #0000000d;

      article {
        .docx-preview_23,
        .docx-preview_cncert1,
        .docx-preview-num-3-0,
        .docx-preview-num-4-0,
        .docx-preview-num-2-0,
        .docx-preview-num-2-1 {
          margin-left: unset !important;
          text-indent: unset !important;
        }
      }

      table {
        width: 100%;
        max-width: 100%;
        margin: 16px 0;
        table-layout: auto;
        border-collapse: collapse;

        td,
        th {
          padding: 8px 12px;
          text-align: left;
          word-wrap: break-word;
          border: 1px solid #ddd;
        }

        th {
          font-weight: bold;
          background-color: #f5f5f5;
        }
      }

      p {
        box-sizing: border-box;
        margin: 20px 0;

        // text-align: justify;
      }

      // 标题样式
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        width: 100%;
        margin: 16px 0 8px;
        font-weight: bold;
        color: #2c3e50;
      }

      // 列表样式
      ul,
      ol {
        box-sizing: border-box;
        width: 100%;
        padding-left: 24px;
        margin: 8px 0;
      }

      li {
        margin: 4px 0;
      }

      // 图片样式
      img {
        display: block;
        max-width: 100%;
        height: auto;
        margin: 16px auto;
      }

      // 图片容器居中
      p img,
      div img {
        display: block;
        margin-right: auto;
        margin-left: auto;
      }

      // 确保所有div元素都充满宽度
      div {
        box-sizing: border-box;
        max-width: 100%;
      }

      // 强制覆盖docx-preview的默认样式
      &.docx-preview {
        padding: 20px !important;

        header {
          height: auto !important;
          border-bottom: 1px solid #d0d0d0;

          p {
            display: flex;
            align-items: center;
            justify-content: center;

            span > div {
              height: inherit !important;
            }
          }
        }

        footer {
          justify-content: center;
          border-top: 1px solid #d0d0d0;

          p {
            display: flex;
            align-items: center;
            justify-content: center;
            width: auto !important;
            text-align: unset !important;
            text-indent: unset !important;

            span > div {
              height: inherit !important;
            }
          }
        }
      }
    }
  }
}

:deep(.docx-preview section[style*='min-height']) {
  min-height: auto !important;
}

:deep(.el-drawer__body) {
  padding: 0 !important;
}

:deep(.el-drawer__header) {
  padding: 16px 20px;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #e4e7ed;
}

.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 20px;
  background: #fafafa;
  border-top: 1px solid #e4e7ed;
}
</style>
