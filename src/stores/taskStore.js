import { createTask } from '@/api'

export const useTaskStore = defineStore(
  'task',
  () => {
    const tasks = ref([])
    const currentTask = ref(null)
    const loading = ref(false)
    const isCreatingNewTask = ref(true)
    const taskLoading = ref(false)
    const taskSelectionType = ref(null)

    const taskCount = computed(() => tasks.value.length)
    const tasksByStatus = computed(() => {
      return {
        pending: tasks.value.filter((task) => task.status === '0'),
        running: tasks.value.filter((task) => task.status === '1'),
        completed: tasks.value.filter((task) => task.status === '9'),
        failed: tasks.value.filter((task) => task.status === '-1'),
        terminated: tasks.value.filter((task) => task.status === '-2')
      }
    })

    const selectTask = (task, selectionType = 'SWITCH_TASK') => {
      currentTask.value = task
      isCreatingNewTask.value = false
      taskSelectionType.value = selectionType
    }

    const clearSelection = () => {
      currentTask.value = null
      isCreatingNewTask.value = false
      taskSelectionType.value = null
    }

    const startNewTask = () => {
      currentTask.value = null
      isCreatingNewTask.value = true
      taskSelectionType.value = null
    }

    const clearTaskSelectionType = () => {
      taskSelectionType.value = null
    }

    // 重置整个store状态（用于退出登录）
    const resetStore = () => {
      tasks.value = []
      currentTask.value = null
      isCreatingNewTask.value = false
      taskSelectionType.value = null
      loading.value = false
      taskLoading.value = false
      console.log('TaskStore 已重置')
    }

    // 创建任务
    const createTaskAsync = async (taskData) => {
      taskLoading.value = true
      try {
        const result = await createTask(taskData)

        if (result.e || !result.d?.taskId) {
          throw new Error(result.m || '任务创建失败')
        }

        const newTask = {
          taskId: result.d.taskId,
          sysName: taskData.sysName,
          sysUrl: taskData.sysUrl,
          status: '1',
          fileName: taskData.fileName,
          presetVulnerability: taskData.presetVulnerability,
          startDate: result.d.createTime,
          endDate: '',
          reportFileName: ''
        }
        tasks.value.unshift(newTask)
        currentTask.value = newTask
        isCreatingNewTask.value = false
        taskSelectionType.value = 'NEW_TASK'
        console.log('任务创建成功，taskId:', newTask.taskId)
        return newTask.taskId
      } catch (e) {
        console.log(e)
        currentTask.value = null
        isCreatingNewTask.value = true
        taskSelectionType.value = null
        return null
      } finally {
        taskLoading.value = false
      }
    }

    // 更新任务状态
    const updateTaskStatus = (taskId, status, additionalData = {}) => {
      const task = tasks.value.find((t) => t.taskId === taskId)
      if (task) {
        task.status = status
        Object.assign(task, additionalData)
        if (currentTask.value && currentTask.value.taskId === taskId) {
          Object.assign(currentTask.value, { status, ...additionalData })
        }
      }
    }

    // 更新任务列表
    const updateTaskList = (newTasks) => {
      tasks.value.splice(0, tasks.value.length, ...(newTasks || []))
      console.log('任务列表已更新，当前任务数量:', tasks.value.length)
    }

    // 删除本地任务数据
    const deleteTask = (taskId) => {
      const taskIndex = tasks.value.findIndex((t) => t.taskId === taskId)
      if (taskIndex > -1) {
        const isCurrentTask = currentTask.value && currentTask.value.taskId === taskId
        tasks.value.splice(taskIndex, 1)
        if (isCurrentTask) {
          currentTask.value = null
          isCreatingNewTask.value = false
          console.log(`删除了当前选中的任务 ${taskId}，已清除选中状态`)
        }
        return { success: true, wasCurrentTask: isCurrentTask }
      }
      return { success: false, wasCurrentTask: false }
    }

    return {
      tasks,
      currentTask,
      loading,
      isCreatingNewTask,
      taskLoading,
      taskSelectionType,
      taskCount,
      tasksByStatus,
      selectTask,
      clearSelection,
      startNewTask,
      updateTaskStatus,
      createTaskAsync,
      updateTaskList,
      clearTaskSelectionType,
      deleteTask,
      resetStore
    }
  }
  // {
  //   persist: {
  //     key: 'intelligent-penetration-task-store',
  //     storage: localStorage,
  //     omit: [
  //       'tasks',
  //       'loading',
  //       'taskLoading',
  //       'taskExecutionStates',
  //       'currentTask',
  //       'isCreatingNewTask',
  //       'taskSelectionType'
  //     ]
  //   }
  // }
)
