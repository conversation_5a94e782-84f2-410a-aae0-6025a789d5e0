import { defineStore } from 'pinia'
import { useTaskStore } from './taskStore'

export const useAuthStore = defineStore(
  'auth',
  () => {
    const userInfo = ref(null)
    const token = ref('')
    const isLoggingOut = ref(false)
    const isLoggedIn = computed(() => !!token.value && !isLoggingOut.value)

    const login = (loginResponse) => {
      token.value = loginResponse.token
      userInfo.value = loginResponse.userInfo
      console.log('用户登录成功:', loginResponse.userInfo?.username)
    }

    const logout = () => {
      isLoggingOut.value = true

      try {
        const taskStore = useTaskStore()
        taskStore.resetStore()

        userInfo.value = null
        token.value = ''

        localStorage.removeItem('intelligent-penetration-auth-store')
        sessionStorage.clear()
        const keysToRemove = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.includes('intelligent-penetration')) {
            keysToRemove.push(key)
          }
        }
        keysToRemove.forEach((key) => localStorage.removeItem(key))

        console.log('用户已登出，所有缓存已清除')
      } catch (error) {
        console.error('登出过程中发生错误:', error)
      } finally {
        nextTick(() => {
          isLoggingOut.value = false
        })
      }
    }

    const checkAuth = () => {
      return isLoggedIn.value
    }

    return {
      userInfo,
      token,
      isLoggedIn,
      isLoggingOut,

      login,
      logout,
      checkAuth
    }
  },
  {
    persist: {
      key: 'intelligent-penetration-auth-store',
      storage: localStorage,
      pick: ['userInfo', 'token']
    }
  }
)
