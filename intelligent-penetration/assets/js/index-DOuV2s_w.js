const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/vue-core-Do3hoBJo.js","assets/js/vendor-BBstUvCG.js","assets/js/date-utils-CE93C0rQ.js","assets/js/common-BlQ_hLMu.js","assets/js/network-crypto-CC4X1yux.js","assets/js/shared/Dashboard/Login-CWbaHWFx.js","assets/js/document-puXNRd0F.js","assets/js/animation-Bzwi2dBh.js","assets/css/vue-core-BmeDYQZL.css"])))=>i.map(i=>d[i]);
import{u as i}from"./vendor-BBstUvCG.js";import{j as m,k as p,l as u,p as c,q as l,_ as f}from"./vue-core-Do3hoBJo.js";import{a as g}from"./common-BlQ_hLMu.js";import"./date-utils-CE93C0rQ.js";import"./shared/Dashboard/Login-CWbaHWFx.js";import"./document-puXNRd0F.js";import"./animation-Bzwi2dBh.js";import"./network-crypto-CC4X1yux.js";const s=m({history:p("/intelligent-penetration/"),routes:[{path:"/",redirect:"/login"},{path:"/login",name:"Login",component:()=>i(()=>import("./vue-core-Do3hoBJo.js").then(e=>e.L),__vite__mapDeps([0,1,2,3,4,5,6,7,8])),meta:{title:"登录",requiresAuth:!1}},{path:"/Dashboard",name:"Dashboard",component:()=>i(()=>import("./vue-core-Do3hoBJo.js").then(e=>e.D),__vite__mapDeps([0,1,2,3,4,5,6,7,8])),meta:{keepAlive:!0,title:"首页看板",requiresAuth:!0}}]});s.beforeEach((e,t,o)=>{const n=g();e.meta.title&&(document.title=`${e.meta.title} - 智能渗透测试系统`),e.meta.requiresAuth?n.isLoggedIn?o():o("/login"):e.path==="/login"&&n.isLoggedIn?o("/Dashboard"):o()});function h(){if(typeof window!="undefined"&&window.performance){const e=performance.getEntriesByType("navigation")[0],t=(o,n)=>{if(!o||!n||o<=0||n<=0)return 0;const a=o-n;return a>=0?Math.round(a):0};return{dns:t(e.domainLookupEnd,e.domainLookupStart),tcp:t(e.connectEnd,e.connectStart),request:t(e.responseEnd,e.requestStart),domParse:t(e.domInteractive,e.responseEnd),whiteScreen:t(e.responseStart,e.navigationStart),firstScreen:t(e.loadEventEnd,e.navigationStart),domContentLoaded:t(e.domContentLoadedEventEnd,e.navigationStart),domReady:t(e.domComplete,e.navigationStart)}}return null}const r=u(f),d=c();d.use(l);r.use(d);r.use(s);r.mount("#app");window.addEventListener("load",()=>{const e=h()});
