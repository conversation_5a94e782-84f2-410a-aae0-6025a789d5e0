var ve=Object.defineProperty,Te=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var q=Object.getOwnPropertySymbols;var Ee=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var z=(e,a,s)=>a in e?ve(e,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[a]=s,A=(e,a)=>{for(var s in a||(a={}))Ee.call(a,s)&&z(e,s,a[s]);if(q)for(var s of q(a))Ie.call(a,s)&&z(e,s,a[s]);return e},X=(e,a)=>Te(e,me(a));var h=(e,a,s)=>new Promise((l,o)=>{var r=c=>{try{v(s.next(c))}catch(f){o(f)}},n=c=>{try{v(s.throw(c))}catch(f){o(f)}},v=c=>c.done?l(c.value):Promise.resolve(c.value).then(r,n);v((s=s.apply(e,a)).next())});import{u as pe,s as ge,g as ye,d as Ne}from"../../common-DIv7hTwn.js";import{r as we}from"../../document-9vhDTAAt.js";import{b as I,c as C,o as ee,g as te,u as he,n as ae,a as De}from"../../vue-core-C7WHuTcW.js";import{g as N}from"../../animation-Bzwi2dBh.js";import{d as p,r as ke,i as Me,a as Re,b as Ae,c as Ce}from"../../date-utils-NfTJjoy9.js";p.locale("zh-cn");p.extend(ke);p.extend(Me);p.extend(Re);p.extend(Ae);p.extend(Ce);function be(e){return e?typeof e=="string"?p(e,"YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD HH:mm:ss"):p(e).format("YYYY-MM-DD HH:mm:ss"):""}function j(){return p().format("YYYY-MM-DD HH:mm:ss")}function Se(e,a=null,s=!1){if(!e)return 0;const l=typeof e=="string"?p(e,"YYYY-MM-DD HH:mm:ss"):p(e),o=a?typeof a=="string"?p(a,"YYYY-MM-DD HH:mm:ss"):p(a):p();if(s){const n=o.diff(l,"second")/60;return Math.max(0,Math.round(n*10)/10)}else{const r=o.diff(l,"minute");return Math.max(0,r)}}function Le(e,a=!1){if(a&&e<60)return`${e} 分钟`;if(e<1)return"0 分钟";const s=Math.floor(e/60),l=Math.floor(e%60);return s>0?l>0?`${s} 小时 ${l} 分钟`:`${s} 小时`:`${Math.floor(e)} 分钟`}function xe(e){if(!e)return"";const a=typeof e=="string"?p(e,"YYYY-MM-DD HH:mm:ss"):p(e),s=p();return a.isSame(s,"day")?"今天 "+a.format("HH:mm"):a.isSame(s.subtract(1,"day"),"day")?"昨天 "+a.format("HH:mm"):a.isSame(s,"year")?a.format("MM-DD HH:mm"):a.format("YYYY-MM-DD HH:mm:ss")}const T={PENDING:"0",RUNNING:"1",COMPLETED:"9",FAILED:"-1",TERMINATED:"-2"},W=(e,a="目标系统")=>{const s={[T.PENDING]:"任务待执行",[T.RUNNING]:"任务正在执行中，请稍后...",[T.COMPLETED]:`已完成关于${a}目标系统的任务执行，报告内容已经生成，请通过以下路径进行查看：`,[T.FAILED]:"任务执行失败，请重新创建任务",[T.TERMINATED]:"任务已终止，请重新新建任务",default:"无法获取任务状态，请检查网络连接"};return s[e]||s.default},i={IDLE:"idle",INITIALIZING:"initializing",EXECUTING:"executing",MONITORING:"monitoring",COMPLETED:"completed",ERROR:"error",TERMINATED:"terminated"},Y={SWITCH_TASK_CHECK_INTERVAL:3e4,NEW_TASK_CHECK_INTERVAL:3e4,PRESET_VULNERABILITY_FALSE_INTERVAL:300*60*1e3,TIME_UPDATE_INTERVAL:1e3};function We(e={}){const{showDecimalTime:a=!1}=e,s=pe(),l=I(i.IDLE),o=I(""),r=I(!1),n=I(!1),v=I(0),c=I(null),f=I(!1),d=I(null),{typeWriterQueue:w}=Oe(o,d);let m=null,E=null;const g=C(()=>n.value),S=C(()=>l.value===i.EXECUTING||l.value===i.MONITORING),ne=C(()=>l.value===i.ERROR),re=C(()=>Le(v.value,a)),F=(t,u=!1)=>u?t.presetVulnerability?Y.NEW_TASK_CHECK_INTERVAL:Y.PRESET_VULNERABILITY_FALSE_INTERVAL:t.presetVulnerability?Y.SWITCH_TASK_CHECK_INTERVAL:Y.PRESET_VULNERABILITY_FALSE_INTERVAL,D=(t,u={})=>{l.value=t,u.task&&(c.value=u.task)},U=()=>{m&&(m.stop(),m=null),E&&(E.stop(),E=null)},L=()=>{U(),d.value&&(d.value.cancelled=!0,d.value=null)},G=(t,u=null)=>{v.value=Se(t,u,a)},K=t=>{m&&(m.stop(),m=null),G(t),m=Q(()=>{if(l.value===i.COMPLETED){m&&(m.stop(),m=null);return}G(t)},Y.TIME_UPDATE_INTERVAL),m.start()},H=(O,P,...k)=>h(null,[O,P,...k],function*(t,u,y={}){const{useTypeWriter:x=!0,updateTaskList:_=!0,detail:M={},showIcon:R=!1}=y;if(D(t,{task:u}),n.value=!1,U(),t===i.COMPLETED){const V=M.endDate||j();G(u.startDate,V)}const B=W(t===i.COMPLETED?T.COMPLETED:t===i.ERROR?T.FAILED:t===i.TERMINATED?T.TERMINATED:t,u==null?void 0:u.sysName);if(x?(yield w(B),t===i.COMPLETED&&R&&(r.value=!0)):(o.value=B,t===i.COMPLETED&&R&&(r.value=!0)),_&&t!==i.TERMINATED){const V=t===i.COMPLETED?T.COMPLETED:t===i.ERROR?T.FAILED:null;V&&s.updateTaskStatus(u.taskId,V,{endDate:M.endDate||j(),reportFileName:M.reportFileName})}}),oe=(P,...k)=>h(null,[P,...k],function*(t,u={},y=!0,O=!0){yield H(i.COMPLETED,t,{useTypeWriter:y,updateTaskList:O,detail:u,showIcon:!0})}),le=(t,u=!0,y=!0)=>h(null,null,function*(){yield H(i.ERROR,t,{useTypeWriter:u,updateTaskList:y})}),ie=()=>h(null,null,function*(){L(),D(i.TERMINATED),n.value=!1;const t=W(T.TERMINATED);yield w(t)}),b=(y,...O)=>h(null,[y,...O],function*(t,u={}){const{isInitialCheck:P=!0,silent:k=!1,showTypeWriterOnComplete:x=!0}=u;try{!k&&P&&(o.value="任务正在执行中，请稍后......",n.value=!0),f.value=!0;const _=t.presetVulnerability?3e4:300*60*1e3;yield new Promise(R=>setTimeout(R,_));const{d:M}=yield ye(t.taskId);if(l.value===i.TERMINATED||l.value===i.COMPLETED)return;if(!M)throw new Error("无法获取任务详情");switch(M.status){case T.COMPLETED:{const R=t.status!==M.status;yield oe(t,M,x,R);break}case T.RUNNING:if(P&&!k){n.value=!0,o.value="任务正在执行中，请稍后...",D(i.MONITORING,{task:t}),K(t.startDate),f.value=!0;const R=F(t,!1);$(t,{interval:R})}else k||(n.value=!0);break;case T.FAILED:{const R=t.status!==M.status;yield le(t,!k,R);break}default:throw new Error(`未知的任务状态: ${M.status}`)}}catch(_){if(!P&&k)return;P&&(D(i.ERROR),n.value=!1,o.value="操作失败，请检查网络连接或重新尝试")}}),$=(t,u={})=>{const{interval:y=Y.SWITCH_TASK_CHECK_INTERVAL,silent:O=!1,showTypeWriterOnComplete:P=!0}=u;U(),E=Q(()=>{if(l.value!==i.MONITORING)return;const k=s.currentTask;!k||!k.taskId||(t.taskId===k.taskId?b(k,{isInitialCheck:!1,silent:O,showTypeWriterOnComplete:P}):(E.stop(),E=null))},y),E.start()},ue=t=>h(null,null,function*(){try{D(i.EXECUTING,{task:t}),L();const u={cancelled:!1};if(d.value=u,yield w("已接收到任务，开始执行，请稍后..."),u.cancelled)return;n.value=!0,K(t.startDate);const y=yield ge(t.taskId);if(!y&&!y.d)throw new Error(y.m);if(u.cancelled)return;if(s.updateTaskStatus(t.taskId,T.RUNNING),f.value=!0,D(i.MONITORING,{task:t}),yield b(t,{isInitialCheck:!0,silent:!0,showTypeWriterOnComplete:!0}),l.value===i.MONITORING){const O=F(t,!0);$(t,{interval:O,silent:!0,showTypeWriterOnComplete:!0})}}catch(u){D(i.ERROR,{task:t}),n.value=!1,o.value=W(T.FAILED),s.updateTaskStatus(t.taskId,T.FAILED),s.clearTaskSelectionType()}finally{d.value=null}}),ce=t=>h(null,null,function*(){var y;if(!t||!t.taskId)throw new Error("无效的任务数据");const u=s.taskSelectionType==="NEW_TASK";if(!(((y=c.value)==null?void 0:y.taskId)===t.taskId&&!u))try{switch(D(i.INITIALIZING,{task:t}),u||t.status===T.RUNNING||c.value&&c.value.status===T.RUNNING?L():d.value&&(d.value.cancelled=!0,d.value=null),t.status===T.RUNNING&&G(t.startDate,t.endDate),t.status){case T.COMPLETED:{yield H(i.COMPLETED,t,{useTypeWriter:!1,updateTaskList:!1,showIcon:!0});break}case T.FAILED:{yield H(i.ERROR,t,{useTypeWriter:u,updateTaskList:!1});break}case T.TERMINATED:{yield H(i.TERMINATED,t,{useTypeWriter:u,updateTaskList:!1});break}case T.RUNNING:{u?yield ue(t):yield b(t,!0);break}default:throw new Error(`未知的任务状态: ${t.status}`)}}catch(O){D(i.ERROR),n.value=!1,o.value="操作失败，请检查网络连接或重新尝试"}finally{}}),fe=()=>{L(),D(i.IDLE),o.value="",r.value=!1,n.value=!1,v.value=0,c.value=null,f.value=!1},de=()=>{L(),D(i.IDLE),n.value=!1,o.value="操作已取消"};return te()&&ee(()=>{L()}),{systemDisplayText:o,isCompleted:r,showLoadingIcon:n,executionTime:v,hasStartedExecution:f,hookState:C(()=>l.value),currentTask:C(()=>c.value),isLoading:g,isExecuting:S,hasError:ne,formattedDuration:re,initializeTaskExecution:ce,resetState:fe,cancel:de,clearTimers:L,handleTaskTermination:ie}}function Fe(){const e=I(null),a=I(null),s=()=>e.value?(N.set(e.value,{y:50,opacity:0,scale:.9}),N.to(e.value,{y:0,opacity:1,scale:1,duration:.5,ease:"back.out(1.7)"}).then()):Promise.resolve(),l=(n=.3)=>a.value?(N.set(a.value,{y:30,opacity:0,scale:.95}),N.to(a.value,{y:0,opacity:1,scale:1,duration:.4,ease:"power2.out",delay:n}).then()):Promise.resolve();return{userMessageRef:e,systemMessageRef:a,animateUserMessage:s,animateSystemMessage:l,animateMessageSequence:()=>h(null,null,function*(){yield s(),yield l(.3)}),resetAnimation:()=>{e.value&&N.set(e.value,{clearProps:"all"}),a.value&&N.set(a.value,{clearProps:"all"})}}}function Ke(){const e=I(!1),a=I(!1),s=I(""),l=he("previewContainer"),o=I(!1),r=C(()=>a.value?"加载中...":(s.value||!o.value,"下载文档")),n=C(()=>!a.value),v=E=>h(null,null,function*(){const g=yield fetch("/intelligent-penetration/api/task/downloadReport",{method:"POST",headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")||""},body:JSON.stringify({taskId:E})});if(!g.ok)throw new Error(`获取文档失败: ${g.status} ${g.statusText}`);return g.arrayBuffer()}),c=()=>{e.value=!0},f=E=>h(null,null,function*(){try{yield Ne(E.taskId)}catch(g){De.error(g.message||"下载失败")}}),d=E=>h(null,null,function*(){a.value=!0,s.value="",o.value=!1;try{if(yield ae(),!l.value)throw new Error("预览容器未找到，请稍后重试");l.value.innerHTML="";const g=yield v(E.taskId);if(!l.value)throw new Error("预览容器在渲染前丢失");yield we(g,l.value,null,{className:"docx-preview",inWrapper:!1,ignoreWidth:!1,ignoreHeight:!0,ignoreFonts:!1,breakPages:!0,ignoreLastRenderedPageBreak:!0,experimental:!1,trimXmlDeclaration:!0,useBase64URL:!1,useMathMLPolyfill:!1,renderChanges:!1,renderComments:!1,renderEndnotes:!0,renderFootnotes:!0,renderHeaders:!0,renderFooters:!0}),o.value=!0}catch(g){s.value=g.message||"文档预览失败，请稍后重试",o.value=!1}finally{a.value=!1}});return{drawerVisible:e,previewLoading:a,previewError:s,previewContainer:l,documentLoaded:o,downloadButtonText:r,canDownload:n,handlePreviewReport:c,handleDownloadReport:f,loadDocumentPreview:d,retryPreview:E=>{d(E)},handleDrawerClose:()=>{e.value=!1,a.value=!1,s.value="",o.value=!1,l.value&&(l.value.innerHTML="")}}}function Oe(e,a){return{typeWriterQueue:(l,o=50)=>h(null,null,function*(){var r;e.value="";for(let n=0;n<l.length;n++){if((r=a.value)!=null&&r.cancelled)return;e.value+=l[n],yield new Promise(v=>setTimeout(v,o))}})}}function Q(e,a=1e3,s={}){const{immediate:l=!1,cleanupOnUnmount:o=!0}=s,r=I(null),n=I(!1),v=I(a),c=I(null),f=()=>{r.value||(n.value=!0,c.value=Date.now(),r.value=setInterval(()=>{c.value=Date.now(),e()},v.value))},d=()=>{r.value&&(clearInterval(r.value),r.value=null,n.value=!1)},w=()=>{if(r.value&&n.value){clearInterval(r.value),r.value=null,n.value=!1;const S=Date.now()-c.value;v.value=Math.max(0,a-S)}},m=()=>{n.value||f()},E=()=>{d(),v.value=a,f()},g=S=>{S!==a&&(d(),v.value=S,n.value&&f())};return l&&f(),o&&te()&&ee(()=>{d()}),{isActive:n,start:f,stop:d,pause:w,resume:m,reset:E,updateDelay:g}}const se=e=>e&&e.nodeType===Node.ELEMENT_NODE&&e.parentNode&&typeof e.getBoundingClientRect=="function",Z=(e,a)=>{if(se(e))try{N.set(e,a)}catch(s){}},J=(e,a)=>{if(!se(e))return N.timeline();try{return N.to(e,a)}catch(s){return N.timeline()}};function $e(){const e=(o={})=>{const{headerRef:r,formRef:n,config:v={}}=o,c={initialY:-30,header:{duration:.8,ease:"power2.out",delay:0},form:{duration:.6,ease:"power2.out",delay:.5},overlap:-.3},f=X(A(A({},c),v),{header:A(A({},c.header),v.header),form:A(A({},c.form),v.form)});if(!(r!=null&&r.value)||!(n!=null&&n.value))return;Z(r.value,{opacity:0,y:f.initialY}),Z(n.value,{opacity:0,y:f.initialY});const d=N.timeline(),w=J(r.value,{opacity:1,y:0,duration:f.header.duration,ease:f.header.ease,delay:f.header.delay});d.add(w);const m=J(n.value,{opacity:1,y:0,duration:f.form.duration,ease:f.form.ease});return d.add(m,f.overlap),d};return{initPageAnimation:e,initStandardPageAnimation:(o,r,n={})=>ae(()=>{e({headerRef:o,formRef:r,config:n})}),createSequenceAnimation:(o=[],r={})=>{const v=A(A({},{initialY:-30,stagger:.3,ease:"power2.out"}),r);if(!o.length)return;const c=o.filter(d=>{var w;return(w=d.ref)==null?void 0:w.value});if(!c.length)return;c.forEach(d=>{N.set(d.ref.value,{opacity:0,y:d.initialY||v.initialY})});const f=N.timeline();return c.forEach((d,w)=>{const m=A({duration:.6,ease:v.ease,delay:0},d.config),E=w===0?0:`+=${v.stagger}`;f.to(d.ref.value,{opacity:1,y:0,duration:m.duration,ease:m.ease,delay:m.delay},E)}),f},resetAnimation:(o=[])=>{o.forEach(r=>{r!=null&&r.value&&N.set(r.value,{opacity:0,y:-30})})}}}function Be(e,a="modelValue",s,l={}){const{passive:o=!1,eventName:r}=l,n=r||(a==="modelValue"?"update:modelValue":`update:${a}`);return C({get(){return e[a]},set(c){o||s(n,c)}})}export{i as H,T,Be as a,Ke as b,We as c,Fe as d,be as f,xe as g,$e as u};
