var ye=Object.defineProperty,Se=Object.defineProperties;var Pe=Object.getOwnPropertyDescriptors;var q=Object.getOwnPropertySymbols;var we=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var J=(a,e,t)=>e in a?ye(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,P=(a,e)=>{for(var t in e||(e={}))we.call(e,t)&&J(a,t,e[t]);if(q)for(var t of q(e))Ce.call(e,t)&&J(a,t,e[t]);return a},B=(a,e)=>Se(a,Pe(e));var w=(a,e,t)=>new Promise((r,s)=>{var n=h=>{try{c(t.next(h))}catch(u){s(u)}},o=h=>{try{c(t.throw(h))}catch(u){s(u)}},c=h=>h.done?r(h.value):Promise.resolve(h.value).then(n,o);c((t=t.apply(a,e)).next())});import{J as Ne}from"./vendor-P5rqJCEK.js";var b;(function(a){a.OfficeDocument="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",a.FontTable="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable",a.Image="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",a.Numbering="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",a.Styles="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",a.StylesWithEffects="http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects",a.Theme="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",a.Settings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings",a.WebSettings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings",a.Hyperlink="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",a.Footnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes",a.Endnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes",a.Footer="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",a.Header="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",a.ExtendedProperties="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",a.CoreProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",a.CustomProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties",a.Comments="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",a.CommentsExtended="http://schemas.microsoft.com/office/2011/relationships/commentsExtended",a.AltChunk="http://schemas.openxmlformats.org/officeDocument/2006/relationships/aFChunk"})(b||(b={}));function Me(a,e){return e.elements(a).map(t=>({id:e.attr(t,"Id"),type:e.attr(t,"Type"),target:e.attr(t,"Target"),targetMode:e.attr(t,"TargetMode")}))}function Ee(a){return a==null?void 0:a.replace(/[ .]+/g,"-").replace(/[&]+/g,"and").toLowerCase()}function j(a){return/^[^"'].*\s.*[^"']$/.test(a)?`'${a}'`:a}function O(a){let e=a.lastIndexOf("/")+1,t=e==0?"":a.substring(0,e),r=e==0?a:a.substring(e);return[t,r]}function _(a,e){try{const t="http://docx/";return new URL(a,t+e).toString().substring(t.length)}catch(t){return`${e}${a}`}}function A(a,e){return a.reduce((t,r)=>(t[e(r)]=r,t),{})}function Ae(a){return new Promise((e,t)=>{const r=new FileReader;r.onloadend=()=>e(r.result),r.onerror=()=>t(),r.readAsDataURL(a)})}function z(a){return a&&typeof a=="object"&&!Array.isArray(a)}function xe(a){return typeof a=="string"||a instanceof String}function I(a,...e){var r;if(!e.length)return a;const t=e.shift();if(z(a)&&z(t))for(const s in t)if(z(t[s])){const n=(r=a[s])!=null?r:a[s]={};I(n,t[s])}else a[s]=t[s];return I(a,...e)}function F(a){return Array.isArray(a)?a:[a]}function Re(a,e,t){return e>a?e:t<a?t:a}const ne={wordml:"http://schemas.openxmlformats.org/wordprocessingml/2006/main"},k={Dxa:{mul:.05,unit:"pt"},Emu:{mul:1/12700,unit:"pt"},FontSize:{mul:.5,unit:"pt"},Border:{mul:.125,unit:"pt",min:.25,max:12},Point:{mul:1,unit:"pt"},Percent:{mul:.02,unit:"%"}};function ie(a,e=k.Dxa){if(a==null||/.+(p[xt]|[%])$/.test(a))return a;var t=parseInt(a)*e.mul;return e.min&&e.max&&(t=Re(t,e.min,e.max)),`${t.toFixed(2)}${e.unit}`}function Be(a,e=!1){switch(a){case"1":return!0;case"0":return!1;case"on":return!0;case"off":return!1;case"true":return!0;case"false":return!1;default:return e}}function le(a,e,t){if(a.namespaceURI!=ne.wordml)return!1;switch(a.localName){case"color":e.color=t.attr(a,"val");break;case"sz":e.fontSize=t.lengthAttr(a,"val",k.FontSize);break;default:return!1}return!0}function Fe(a,e=!1){e&&(a=a.replace(/<[?].*[?]>/,"")),a=Te(a);const t=new DOMParser().parseFromString(a,"application/xml"),r=Le(t);if(r)throw new Error(r);return t}function Le(a){var e;return(e=a.getElementsByTagName("parsererror")[0])==null?void 0:e.textContent}function Te(a){return a.charCodeAt(0)===65279?a.substring(1):a}function $e(a){return new XMLSerializer().serializeToString(a)}class oe{elements(e,t=null){const r=[];for(let s=0,n=e.childNodes.length;s<n;s++){let o=e.childNodes.item(s);o.nodeType==1&&(t==null||o.localName==t)&&r.push(o)}return r}element(e,t){for(let r=0,s=e.childNodes.length;r<s;r++){let n=e.childNodes.item(r);if(n.nodeType==1&&n.localName==t)return n}return null}elementAttr(e,t,r){var s=this.element(e,t);return s?this.attr(s,r):void 0}attrs(e){return Array.from(e.attributes)}attr(e,t){for(let r=0,s=e.attributes.length;r<s;r++){let n=e.attributes.item(r);if(n.localName==t)return n.value}return null}intAttr(e,t,r=null){var s=this.attr(e,t);return s?parseInt(s):r}hexAttr(e,t,r=null){var s=this.attr(e,t);return s?parseInt(s,16):r}floatAttr(e,t,r=null){var s=this.attr(e,t);return s?parseFloat(s):r}boolAttr(e,t,r=null){return Be(this.attr(e,t),r)}lengthAttr(e,t,r=k.Dxa){return ie(this.attr(e,t),r)}}const i=new oe;class C{constructor(e,t){this._package=e,this.path=t}load(){return w(this,null,function*(){this.rels=yield this._package.loadRelationships(this.path);const e=yield this._package.load(this.path),t=this._package.parseXmlDocument(e);this._package.options.keepOrigin&&(this._xmlDocument=t),this.parseXml(t.firstElementChild)})}save(){this._package.update(this.path,$e(this._xmlDocument))}parseXml(e){}}const De={embedRegular:"regular",embedBold:"bold",embedItalic:"italic",embedBoldItalic:"boldItalic"};function Oe(a,e){return e.elements(a).map(t=>He(t,e))}function He(a,e){let t={name:e.attr(a,"name"),embedFontRefs:[]};for(let r of e.elements(a))switch(r.localName){case"family":t.family=e.attr(r,"val");break;case"altName":t.altName=e.attr(r,"val");break;case"embedRegular":case"embedBold":case"embedItalic":case"embedBoldItalic":t.embedFontRefs.push(Ie(r,e));break}return t}function Ie(a,e){return{id:e.attr(a,"id"),key:e.attr(a,"fontKey"),type:De[a.localName]}}class _e extends C{parseXml(e){this.fonts=Oe(e,this._package.xmlParser)}}class W{constructor(e,t){this._zip=e,this.options=t,this.xmlParser=new oe}get(e){var r;const t=ze(e);return(r=this._zip.files[t])!=null?r:this._zip.files[t.replace(/\//g,"\\")]}update(e,t){this._zip.file(e,t)}static load(e,t){return w(this,null,function*(){const r=yield Ne.loadAsync(e);return new W(r,t)})}save(e="blob"){return this._zip.generateAsync({type:e})}load(e,t="string"){var r,s;return(s=(r=this.get(e))==null?void 0:r.async(t))!=null?s:Promise.resolve(null)}loadRelationships(e=null){return w(this,null,function*(){let t="_rels/.rels";if(e!=null){const[s,n]=O(e);t=`${s}_rels/${n}.rels`}const r=yield this.load(t);return r?Me(this.parseXmlDocument(r).firstElementChild,this.xmlParser):null})}parseXmlDocument(e){return Fe(e,this.options.trimXmlDeclaration)}}function ze(a){return a.startsWith("/")?a.substr(1):a}class Ve extends C{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.body=this._documentParser.parseDocumentFile(e)}}function D(a,e){return{type:e.attr(a,"val"),color:e.attr(a,"color"),size:e.lengthAttr(a,"sz",k.Border),offset:e.lengthAttr(a,"space",k.Point),frame:e.boolAttr(a,"frame"),shadow:e.boolAttr(a,"shadow")}}function je(a,e){var t={};for(let r of e.elements(a))switch(r.localName){case"left":t.left=D(r,e);break;case"top":t.top=D(r,e);break;case"right":t.right=D(r,e);break;case"bottom":t.bottom=D(r,e);break}return t}var Z;(function(a){a.Continuous="continuous",a.NextPage="nextPage",a.NextColumn="nextColumn",a.EvenPage="evenPage",a.OddPage="oddPage"})(Z||(Z={}));function ce(a,e=i){var r,s;var t={};for(let n of e.elements(a))switch(n.localName){case"pgSz":t.pageSize={width:e.lengthAttr(n,"w"),height:e.lengthAttr(n,"h"),orientation:e.attr(n,"orient")};break;case"type":t.type=e.attr(n,"val");break;case"pgMar":t.pageMargins={left:e.lengthAttr(n,"left"),right:e.lengthAttr(n,"right"),top:e.lengthAttr(n,"top"),bottom:e.lengthAttr(n,"bottom"),header:e.lengthAttr(n,"header"),footer:e.lengthAttr(n,"footer"),gutter:e.lengthAttr(n,"gutter")};break;case"cols":t.columns=We(n,e);break;case"headerReference":((r=t.headerRefs)!=null?r:t.headerRefs=[]).push(Y(n,e));break;case"footerReference":((s=t.footerRefs)!=null?s:t.footerRefs=[]).push(Y(n,e));break;case"titlePg":t.titlePage=e.boolAttr(n,"val",!0);break;case"pgBorders":t.pageBorders=je(n,e);break;case"pgNumType":t.pageNumber=Xe(n,e);break}return t}function We(a,e){return{numberOfColumns:e.intAttr(a,"num"),space:e.lengthAttr(a,"space"),separator:e.boolAttr(a,"sep"),equalWidth:e.boolAttr(a,"equalWidth",!0),columns:e.elements(a,"col").map(t=>({width:e.lengthAttr(t,"w"),space:e.lengthAttr(t,"space")}))}}function Xe(a,e){return{chapSep:e.attr(a,"chapSep"),chapStyle:e.attr(a,"chapStyle"),format:e.attr(a,"fmt"),start:e.intAttr(a,"start")}}function Y(a,e){return{id:e.attr(a,"id"),type:e.attr(a,"type")}}function Ue(a,e){return{before:e.lengthAttr(a,"before"),after:e.lengthAttr(a,"after"),line:e.intAttr(a,"line"),lineRule:e.attr(a,"lineRule")}}function X(a,e){let t={};for(let r of e.elements(a))Ge(r,t,e);return t}function Ge(a,e,t){return!!le(a,e,t)}function he(a,e){let t={};for(let r of e.elements(a))ue(r,t,e);return t}function ue(a,e,t){if(a.namespaceURI!=ne.wordml)return!1;if(le(a,e,t))return!0;switch(a.localName){case"tabs":e.tabs=qe(a,t);break;case"sectPr":e.sectionProps=ce(a,t);break;case"numPr":e.numbering=Je(a,t);break;case"spacing":return e.lineSpacing=Ue(a,t),!1;case"textAlignment":return e.textAlignment=t.attr(a,"val"),!1;case"keepLines":e.keepLines=t.boolAttr(a,"val",!0);break;case"keepNext":e.keepNext=t.boolAttr(a,"val",!0);break;case"pageBreakBefore":e.pageBreakBefore=t.boolAttr(a,"val",!0);break;case"outlineLvl":e.outlineLevel=t.intAttr(a,"val");break;case"pStyle":e.styleName=t.attr(a,"val");break;case"rPr":e.runProps=X(a,t);break;default:return!1}return!0}function qe(a,e){return e.elements(a,"tab").map(t=>({position:e.lengthAttr(t,"pos"),leader:e.attr(t,"leader"),style:e.attr(t,"val")}))}function Je(a,e){var t={};for(let r of e.elements(a))switch(r.localName){case"numId":t.id=e.attr(r,"val");break;case"ilvl":t.level=e.intAttr(r,"val");break}return t}function Ze(a,e){let t={numberings:[],abstractNumberings:[],bulletPictures:[]};for(let r of e.elements(a))switch(r.localName){case"num":t.numberings.push(Ye(r,e));break;case"abstractNum":t.abstractNumberings.push(Ke(r,e));break;case"numPicBullet":t.bulletPictures.push(et(r,e));break}return t}function Ye(a,e){let t={id:e.attr(a,"numId"),overrides:[]};for(let r of e.elements(a))switch(r.localName){case"abstractNumId":t.abstractId=e.attr(r,"val");break;case"lvlOverride":t.overrides.push(Qe(r,e));break}return t}function Ke(a,e){let t={id:e.attr(a,"abstractNumId"),levels:[]};for(let r of e.elements(a))switch(r.localName){case"name":t.name=e.attr(r,"val");break;case"multiLevelType":t.multiLevelType=e.attr(r,"val");break;case"numStyleLink":t.numberingStyleLink=e.attr(r,"val");break;case"styleLink":t.styleLink=e.attr(r,"val");break;case"lvl":t.levels.push(de(r,e));break}return t}function de(a,e){let t={level:e.intAttr(a,"ilvl")};for(let r of e.elements(a))switch(r.localName){case"start":t.start=e.attr(r,"val");break;case"lvlRestart":t.restart=e.intAttr(r,"val");break;case"numFmt":t.format=e.attr(r,"val");break;case"lvlText":t.text=e.attr(r,"val");break;case"lvlJc":t.justification=e.attr(r,"val");break;case"lvlPicBulletId":t.bulletPictureId=e.attr(r,"val");break;case"pStyle":t.paragraphStyle=e.attr(r,"val");break;case"pPr":t.paragraphProps=he(r,e);break;case"rPr":t.runProps=X(r,e);break}return t}function Qe(a,e){let t={level:e.intAttr(a,"ilvl")};for(let r of e.elements(a))switch(r.localName){case"startOverride":t.start=e.intAttr(r,"val");break;case"lvl":t.numberingLevel=de(r,e);break}return t}function et(a,e){var t=e.element(a,"pict"),r=t&&e.element(t,"shape"),s=r&&e.element(r,"imagedata");return s?{id:e.attr(a,"numPicBulletId"),referenceId:e.attr(s,"id"),style:e.attr(r,"style")}:null}class tt extends C{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){Object.assign(this,Ze(e,this._package.xmlParser)),this.domNumberings=this._documentParser.parseNumberingFile(e)}}class rt extends C{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.styles=this._documentParser.parseStylesFile(e)}}var l;(function(a){a.Document="document",a.Paragraph="paragraph",a.Run="run",a.Break="break",a.NoBreakHyphen="noBreakHyphen",a.Table="table",a.Row="row",a.Cell="cell",a.Hyperlink="hyperlink",a.SmartTag="smartTag",a.Drawing="drawing",a.Image="image",a.Text="text",a.Tab="tab",a.Symbol="symbol",a.BookmarkStart="bookmarkStart",a.BookmarkEnd="bookmarkEnd",a.Footer="footer",a.Header="header",a.FootnoteReference="footnoteReference",a.EndnoteReference="endnoteReference",a.Footnote="footnote",a.Endnote="endnote",a.SimpleField="simpleField",a.ComplexField="complexField",a.Instruction="instruction",a.VmlPicture="vmlPicture",a.MmlMath="mmlMath",a.MmlMathParagraph="mmlMathParagraph",a.MmlFraction="mmlFraction",a.MmlFunction="mmlFunction",a.MmlFunctionName="mmlFunctionName",a.MmlNumerator="mmlNumerator",a.MmlDenominator="mmlDenominator",a.MmlRadical="mmlRadical",a.MmlBase="mmlBase",a.MmlDegree="mmlDegree",a.MmlSuperscript="mmlSuperscript",a.MmlSubscript="mmlSubscript",a.MmlPreSubSuper="mmlPreSubSuper",a.MmlSubArgument="mmlSubArgument",a.MmlSuperArgument="mmlSuperArgument",a.MmlNary="mmlNary",a.MmlDelimiter="mmlDelimiter",a.MmlRun="mmlRun",a.MmlEquationArray="mmlEquationArray",a.MmlLimit="mmlLimit",a.MmlLimitLower="mmlLimitLower",a.MmlMatrix="mmlMatrix",a.MmlMatrixRow="mmlMatrixRow",a.MmlBox="mmlBox",a.MmlBar="mmlBar",a.MmlGroupChar="mmlGroupChar",a.VmlElement="vmlElement",a.Inserted="inserted",a.Deleted="deleted",a.DeletedText="deletedText",a.Comment="comment",a.CommentReference="commentReference",a.CommentRangeStart="commentRangeStart",a.CommentRangeEnd="commentRangeEnd",a.AltChunk="altChunk"})(l||(l={}));class x{constructor(){this.children=[],this.cssStyle={}}}class at extends x{constructor(){super(...arguments),this.type=l.Header}}class st extends x{constructor(){super(...arguments),this.type=l.Footer}}class pe extends C{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.rootElement=this.createRootElement(),this.rootElement.children=this._documentParser.parseBodyElements(e)}}class nt extends pe{createRootElement(){return new at}}class it extends pe{createRootElement(){return new st}}function lt(a,e){const t={};for(let r of e.elements(a))switch(r.localName){case"Template":t.template=r.textContent;break;case"Pages":t.pages=L(r.textContent);break;case"Words":t.words=L(r.textContent);break;case"Characters":t.characters=L(r.textContent);break;case"Application":t.application=r.textContent;break;case"Lines":t.lines=L(r.textContent);break;case"Paragraphs":t.paragraphs=L(r.textContent);break;case"Company":t.company=r.textContent;break;case"AppVersion":t.appVersion=r.textContent;break}return t}function L(a){if(typeof a!="undefined")return parseInt(a)}class ot extends C{parseXml(e){this.props=lt(e,this._package.xmlParser)}}function ct(a,e){const t={};for(let r of e.elements(a))switch(r.localName){case"title":t.title=r.textContent;break;case"description":t.description=r.textContent;break;case"subject":t.subject=r.textContent;break;case"creator":t.creator=r.textContent;break;case"keywords":t.keywords=r.textContent;break;case"language":t.language=r.textContent;break;case"lastModifiedBy":t.lastModifiedBy=r.textContent;break;case"revision":r.textContent&&(t.revision=parseInt(r.textContent));break}return t}class ht extends C{parseXml(e){this.props=ct(e,this._package.xmlParser)}}class ut{}function dt(a,e){var t=new ut,r=e.element(a,"themeElements");for(let s of e.elements(r))switch(s.localName){case"clrScheme":t.colorScheme=pt(s,e);break;case"fontScheme":t.fontScheme=mt(s,e);break}return t}function pt(a,e){var t={name:e.attr(a,"name"),colors:{}};for(let n of e.elements(a)){var r=e.element(n,"srgbClr"),s=e.element(n,"sysClr");r?t.colors[n.localName]=e.attr(r,"val"):s&&(t.colors[n.localName]=e.attr(s,"lastClr"))}return t}function mt(a,e){var t={name:e.attr(a,"name")};for(let r of e.elements(a))switch(r.localName){case"majorFont":t.majorFont=K(r,e);break;case"minorFont":t.minorFont=K(r,e);break}return t}function K(a,e){return{latinTypeface:e.elementAttr(a,"latin","typeface"),eaTypeface:e.elementAttr(a,"ea","typeface"),csTypeface:e.elementAttr(a,"cs","typeface")}}class ft extends C{constructor(e,t){super(e,t)}parseXml(e){this.theme=dt(e,this._package.xmlParser)}}class me{}class gt extends me{constructor(){super(...arguments),this.type=l.Footnote}}class bt extends me{constructor(){super(...arguments),this.type=l.Endnote}}class fe extends C{constructor(e,t,r){super(e,t),this._documentParser=r}}class kt extends fe{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"footnote",gt)}}class vt extends fe{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"endnote",bt)}}function yt(a,e){var t={};for(let r of e.elements(a))switch(r.localName){case"defaultTabStop":t.defaultTabStop=e.lengthAttr(r,"val");break;case"footnotePr":t.footnoteProps=Q(r,e);break;case"endnotePr":t.endnoteProps=Q(r,e);break;case"autoHyphenation":t.autoHyphenation=e.boolAttr(r,"val");break}return t}function Q(a,e){var t={defaultNoteIds:[]};for(let r of e.elements(a))switch(r.localName){case"numFmt":t.nummeringFormat=e.attr(r,"val");break;case"footnote":case"endnote":t.defaultNoteIds.push(e.attr(r,"id"));break}return t}class St extends C{constructor(e,t){super(e,t)}parseXml(e){this.settings=yt(e,this._package.xmlParser)}}function Pt(a,e){return e.elements(a,"property").map(t=>{const r=t.firstChild;return{formatId:e.attr(t,"fmtid"),name:e.attr(t,"name"),type:r.nodeName,value:r.textContent}})}class wt extends C{parseXml(e){this.props=Pt(e,this._package.xmlParser)}}class Ct extends C{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.comments=this._documentParser.parseComments(e),this.commentMap=A(this.comments,t=>t.id)}}class Nt extends C{constructor(e,t){super(e,t),this.comments=[]}parseXml(e){const t=this._package.xmlParser;for(let r of t.elements(e,"commentEx"))this.comments.push({paraId:t.attr(r,"paraId"),paraIdParent:t.attr(r,"paraIdParent"),done:t.boolAttr(r,"done")});this.commentMap=A(this.comments,r=>r.paraId)}}const Mt=[{type:b.OfficeDocument,target:"word/document.xml"},{type:b.ExtendedProperties,target:"docProps/app.xml"},{type:b.CoreProperties,target:"docProps/core.xml"},{type:b.CustomProperties,target:"docProps/custom.xml"}];class U{constructor(){this.parts=[],this.partsMap={}}static load(e,t,r){return w(this,null,function*(){var s=new U;return s._options=r,s._parser=t,s._package=yield W.load(e,r),s.rels=yield s._package.loadRelationships(),yield Promise.all(Mt.map(n=>{var c;const o=(c=s.rels.find(h=>h.type===n.type))!=null?c:n;return s.loadRelationshipPart(o.target,o.type)})),s})}save(e="blob"){return this._package.save(e)}loadRelationshipPart(e,t){return w(this,null,function*(){var s;if(this.partsMap[e])return this.partsMap[e];if(!this._package.get(e))return null;let r=null;switch(t){case b.OfficeDocument:this.documentPart=r=new Ve(this._package,e,this._parser);break;case b.FontTable:this.fontTablePart=r=new _e(this._package,e);break;case b.Numbering:this.numberingPart=r=new tt(this._package,e,this._parser);break;case b.Styles:this.stylesPart=r=new rt(this._package,e,this._parser);break;case b.Theme:this.themePart=r=new ft(this._package,e);break;case b.Footnotes:this.footnotesPart=r=new kt(this._package,e,this._parser);break;case b.Endnotes:this.endnotesPart=r=new vt(this._package,e,this._parser);break;case b.Footer:r=new it(this._package,e,this._parser);break;case b.Header:r=new nt(this._package,e,this._parser);break;case b.CoreProperties:this.corePropsPart=r=new ht(this._package,e);break;case b.ExtendedProperties:this.extendedPropsPart=r=new ot(this._package,e);break;case b.CustomProperties:r=new wt(this._package,e);break;case b.Settings:this.settingsPart=r=new St(this._package,e);break;case b.Comments:this.commentsPart=r=new Ct(this._package,e,this._parser);break;case b.CommentsExtended:this.commentsExtendedPart=r=new Nt(this._package,e);break}if(r==null)return Promise.resolve(null);if(this.partsMap[e]=r,this.parts.push(r),yield r.load(),((s=r.rels)==null?void 0:s.length)>0){const[n]=O(r.path);yield Promise.all(r.rels.map(o=>this.loadRelationshipPart(_(o.target,n),o.type)))}return r})}loadDocumentImage(e,t){return w(this,null,function*(){const r=yield this.loadResource(t!=null?t:this.documentPart,e,"blob");return this.blobToURL(r)})}loadNumberingImage(e){return w(this,null,function*(){const t=yield this.loadResource(this.numberingPart,e,"blob");return this.blobToURL(t)})}loadFont(e,t){return w(this,null,function*(){const r=yield this.loadResource(this.fontTablePart,e,"uint8array");return r&&this.blobToURL(new Blob([Et(r,t)]))})}loadAltChunk(e,t){return w(this,null,function*(){return yield this.loadResource(t!=null?t:this.documentPart,e,"string")})}blobToURL(e){return e?this._options.useBase64URL?Ae(e):URL.createObjectURL(e):null}findPartByRelId(e,t=null){var n;var r=((n=t.rels)!=null?n:this.rels).find(o=>o.id==e);const s=t?O(t.path)[0]:"";return r?this.partsMap[_(r.target,s)]:null}getPathById(e,t){const r=e.rels.find(n=>n.id==t),[s]=O(e.path);return r?_(r.target,s):null}loadResource(e,t,r){const s=this.getPathById(e,t);return s?this._package.load(s,r):Promise.resolve(null)}}function Et(a,e){const r=e.replace(/{|}|-/g,""),s=new Array(16);for(let n=0;n<16;n++)s[16-n-1]=parseInt(r.substr(n*2,2),16);for(let n=0;n<32;n++)a[n]=a[n]^s[n%16];return a}function At(a,e){return{type:l.BookmarkStart,id:e.attr(a,"id"),name:e.attr(a,"name"),colFirst:e.intAttr(a,"colFirst"),colLast:e.intAttr(a,"colLast")}}function xt(a,e){return{type:l.BookmarkEnd,id:e.attr(a,"id")}}class Rt extends x{constructor(){super(...arguments),this.type=l.VmlElement,this.attrs={}}}function ge(a,e){var t=new Rt;switch(a.localName){case"rect":t.tagName="rect",Object.assign(t.attrs,{width:"100%",height:"100%"});break;case"oval":t.tagName="ellipse",Object.assign(t.attrs,{cx:"50%",cy:"50%",rx:"50%",ry:"50%"});break;case"line":t.tagName="line";break;case"shape":t.tagName="g";break;case"textbox":t.tagName="foreignObject",Object.assign(t.attrs,{width:"100%",height:"100%"});break;default:return null}for(const r of i.attrs(a))switch(r.localName){case"style":t.cssStyleText=r.value;break;case"fillcolor":t.attrs.fill=r.value;break;case"from":const[s,n]=ee(r.value);Object.assign(t.attrs,{x1:s,y1:n});break;case"to":const[o,c]=ee(r.value);Object.assign(t.attrs,{x2:o,y2:c});break}for(const r of i.elements(a))switch(r.localName){case"stroke":Object.assign(t.attrs,Bt(r));break;case"fill":Object.assign(t.attrs,Ft());break;case"imagedata":t.tagName="image",Object.assign(t.attrs,{width:"100%",height:"100%"}),t.imageHref={id:i.attr(r,"id"),title:i.attr(r,"title")};break;case"txbxContent":t.children.push(...e.parseBodyElements(r));break;default:const s=ge(r,e);s&&t.children.push(s);break}return t}function Bt(a){var e;return{stroke:i.attr(a,"color"),"stroke-width":(e=i.lengthAttr(a,"weight",k.Emu))!=null?e:"1px"}}function Ft(a){return{}}function ee(a){return a.split(",")}class Lt extends x{constructor(){super(...arguments),this.type=l.Comment}}class Tt extends x{constructor(e){super(),this.id=e,this.type=l.CommentReference}}class $t extends x{constructor(e){super(),this.id=e,this.type=l.CommentRangeStart}}class Dt extends x{constructor(e){super(),this.id=e,this.type=l.CommentRangeEnd}}var H={shd:"inherit",color:"black",borderColor:"black",highlight:"transparent"};const Ot=[],te={oMath:l.MmlMath,oMathPara:l.MmlMathParagraph,f:l.MmlFraction,func:l.MmlFunction,fName:l.MmlFunctionName,num:l.MmlNumerator,den:l.MmlDenominator,rad:l.MmlRadical,deg:l.MmlDegree,e:l.MmlBase,sSup:l.MmlSuperscript,sSub:l.MmlSubscript,sPre:l.MmlPreSubSuper,sup:l.MmlSuperArgument,sub:l.MmlSubArgument,d:l.MmlDelimiter,nary:l.MmlNary,eqArr:l.MmlEquationArray,lim:l.MmlLimit,limLow:l.MmlLimitLower,m:l.MmlMatrix,mr:l.MmlMatrixRow,box:l.MmlBox,bar:l.MmlBar,groupChr:l.MmlGroupChar};class Ht{constructor(e){this.options=P({ignoreWidth:!1,debug:!1},e)}parseNotes(e,t,r){var s=[];for(let n of i.elements(e,t)){const o=new r;o.id=i.attr(n,"id"),o.noteType=i.attr(n,"type"),o.children=this.parseBodyElements(n),s.push(o)}return s}parseComments(e){var t=[];for(let r of i.elements(e,"comment")){const s=new Lt;s.id=i.attr(r,"id"),s.author=i.attr(r,"author"),s.initials=i.attr(r,"initials"),s.date=i.attr(r,"date"),s.children=this.parseBodyElements(r),t.push(s)}return t}parseDocumentFile(e){var t=i.element(e,"body"),r=i.element(e,"background"),s=i.element(t,"sectPr");return{type:l.Document,children:this.parseBodyElements(t),props:s?ce(s,i):{},cssStyle:r?this.parseBackground(r):{}}}parseBackground(e){var t={},r=g.colorAttr(e,"color");return r&&(t["background-color"]=r),t}parseBodyElements(e){var t=[];for(let r of i.elements(e))switch(r.localName){case"p":t.push(this.parseParagraph(r));break;case"altChunk":t.push(this.parseAltChunk(r));break;case"tbl":t.push(this.parseTable(r));break;case"sdt":t.push(...this.parseSdt(r,s=>this.parseBodyElements(s)));break}return t}parseStylesFile(e){var t=[];return g.foreach(e,r=>{switch(r.localName){case"style":t.push(this.parseStyle(r));break;case"docDefaults":t.push(this.parseDefaultStyles(r));break}}),t}parseDefaultStyles(e){var t={id:null,name:null,target:null,basedOn:null,styles:[]};return g.foreach(e,r=>{switch(r.localName){case"rPrDefault":var s=i.element(r,"rPr");s&&t.styles.push({target:"span",values:this.parseDefaultProperties(s,{})});break;case"pPrDefault":var n=i.element(r,"pPr");n&&t.styles.push({target:"p",values:this.parseDefaultProperties(n,{})});break}}),t}parseStyle(e){var t={id:i.attr(e,"styleId"),isDefault:i.boolAttr(e,"default"),name:null,target:null,basedOn:null,styles:[],linked:null};switch(i.attr(e,"type")){case"paragraph":t.target="p";break;case"table":t.target="table";break;case"character":t.target="span";break}return g.foreach(e,r=>{switch(r.localName){case"basedOn":t.basedOn=i.attr(r,"val");break;case"name":t.name=i.attr(r,"val");break;case"link":t.linked=i.attr(r,"val");break;case"next":t.next=i.attr(r,"val");break;case"aliases":t.aliases=i.attr(r,"val").split(",");break;case"pPr":t.styles.push({target:"p",values:this.parseDefaultProperties(r,{})}),t.paragraphProps=he(r,i);break;case"rPr":t.styles.push({target:"span",values:this.parseDefaultProperties(r,{})}),t.runProps=X(r,i);break;case"tblPr":case"tcPr":t.styles.push({target:"td",values:this.parseDefaultProperties(r,{})});break;case"tblStylePr":for(let s of this.parseTableStyle(r))t.styles.push(s);break;case"rsid":case"qFormat":case"hidden":case"semiHidden":case"unhideWhenUsed":case"autoRedefine":case"uiPriority":break;default:this.options.debug}}),t}parseTableStyle(e){var t=[],r=i.attr(e,"type"),s="",n="";switch(r){case"firstRow":n=".first-row",s="tr.first-row td";break;case"lastRow":n=".last-row",s="tr.last-row td";break;case"firstCol":n=".first-col",s="td.first-col";break;case"lastCol":n=".last-col",s="td.last-col";break;case"band1Vert":n=":not(.no-vband)",s="td.odd-col";break;case"band2Vert":n=":not(.no-vband)",s="td.even-col";break;case"band1Horz":n=":not(.no-hband)",s="tr.odd-row";break;case"band2Horz":n=":not(.no-hband)",s="tr.even-row";break;default:return[]}return g.foreach(e,o=>{switch(o.localName){case"pPr":t.push({target:`${s} p`,mod:n,values:this.parseDefaultProperties(o,{})});break;case"rPr":t.push({target:`${s} span`,mod:n,values:this.parseDefaultProperties(o,{})});break;case"tblPr":case"tcPr":t.push({target:s,mod:n,values:this.parseDefaultProperties(o,{})});break}}),t}parseNumberingFile(e){var t=[],r={},s=[];return g.foreach(e,n=>{switch(n.localName){case"abstractNum":this.parseAbstractNumbering(n,s).forEach(h=>t.push(h));break;case"numPicBullet":s.push(this.parseNumberingPicBullet(n));break;case"num":var o=i.attr(n,"numId"),c=i.elementAttr(n,"abstractNumId","val");r[c]=o;break}}),t.forEach(n=>n.id=r[n.id]),t}parseNumberingPicBullet(e){var t=i.element(e,"pict"),r=t&&i.element(t,"shape"),s=r&&i.element(r,"imagedata");return s?{id:i.intAttr(e,"numPicBulletId"),src:i.attr(s,"id"),style:i.attr(r,"style")}:null}parseAbstractNumbering(e,t){var r=[],s=i.attr(e,"abstractNumId");return g.foreach(e,n=>{switch(n.localName){case"lvl":r.push(this.parseNumberingLevel(s,n,t));break}}),r}parseNumberingLevel(e,t,r){var s={id:e,level:i.intAttr(t,"ilvl"),start:1,pStyleName:void 0,pStyle:{},rStyle:{},suff:"tab"};return g.foreach(t,n=>{switch(n.localName){case"start":s.start=i.intAttr(n,"val");break;case"pPr":this.parseDefaultProperties(n,s.pStyle);break;case"rPr":this.parseDefaultProperties(n,s.rStyle);break;case"lvlPicBulletId":var o=i.intAttr(n,"val");s.bullet=r.find(c=>(c==null?void 0:c.id)==o);break;case"lvlText":s.levelText=i.attr(n,"val");break;case"pStyle":s.pStyleName=i.attr(n,"val");break;case"numFmt":s.format=i.attr(n,"val");break;case"suff":s.suff=i.attr(n,"val");break}}),s}parseSdt(e,t){const r=i.element(e,"sdtContent");return r?t(r):[]}parseInserted(e,t){var r,s;return{type:l.Inserted,children:(s=(r=t(e))==null?void 0:r.children)!=null?s:[]}}parseDeleted(e,t){var r,s;return{type:l.Deleted,children:(s=(r=t(e))==null?void 0:r.children)!=null?s:[]}}parseAltChunk(e){return{type:l.AltChunk,children:[],id:i.attr(e,"id")}}parseParagraph(e){var t={type:l.Paragraph,children:[]};for(let r of i.elements(e))switch(r.localName){case"pPr":this.parseParagraphProperties(r,t);break;case"r":t.children.push(this.parseRun(r,t));break;case"hyperlink":t.children.push(this.parseHyperlink(r,t));break;case"smartTag":t.children.push(this.parseSmartTag(r,t));break;case"bookmarkStart":t.children.push(At(r,i));break;case"bookmarkEnd":t.children.push(xt(r,i));break;case"commentRangeStart":t.children.push(new $t(i.attr(r,"id")));break;case"commentRangeEnd":t.children.push(new Dt(i.attr(r,"id")));break;case"oMath":case"oMathPara":t.children.push(this.parseMathElement(r));break;case"sdt":t.children.push(...this.parseSdt(r,s=>this.parseParagraph(s).children));break;case"ins":t.children.push(this.parseInserted(r,s=>this.parseParagraph(s)));break;case"del":t.children.push(this.parseDeleted(r,s=>this.parseParagraph(s)));break}return t}parseParagraphProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,r=>{if(ue(r,t,i))return!0;switch(r.localName){case"pStyle":t.styleName=i.attr(r,"val");break;case"cnfStyle":t.className=f.classNameOfCnfStyle(r);break;case"framePr":this.parseFrame(r,t);break;case"rPr":break;default:return!1}return!0})}parseFrame(e,t){var r=i.attr(e,"dropCap");r=="drop"&&(t.cssStyle.float="left")}parseHyperlink(e,t){var r={type:l.Hyperlink,parent:t,children:[]};return r.anchor=i.attr(e,"anchor"),r.id=i.attr(e,"id"),g.foreach(e,s=>{switch(s.localName){case"r":r.children.push(this.parseRun(s,r));break}}),r}parseSmartTag(e,t){var r={type:l.SmartTag,parent:t,children:[]},s=i.attr(e,"uri"),n=i.attr(e,"element");return s&&(r.uri=s),n&&(r.element=n),g.foreach(e,o=>{switch(o.localName){case"r":r.children.push(this.parseRun(o,r));break}}),r}parseRun(e,t){var r={type:l.Run,parent:t,children:[]};return g.foreach(e,s=>{switch(s=this.checkAlternateContent(s),s.localName){case"t":r.children.push({type:l.Text,text:s.textContent});break;case"delText":r.children.push({type:l.DeletedText,text:s.textContent});break;case"commentReference":r.children.push(new Tt(i.attr(s,"id")));break;case"fldSimple":r.children.push({type:l.SimpleField,instruction:i.attr(s,"instr"),lock:i.boolAttr(s,"lock",!1),dirty:i.boolAttr(s,"dirty",!1)});break;case"instrText":r.fieldRun=!0,r.children.push({type:l.Instruction,text:s.textContent});break;case"fldChar":r.fieldRun=!0,r.children.push({type:l.ComplexField,charType:i.attr(s,"fldCharType"),lock:i.boolAttr(s,"lock",!1),dirty:i.boolAttr(s,"dirty",!1)});break;case"noBreakHyphen":r.children.push({type:l.NoBreakHyphen});break;case"br":r.children.push({type:l.Break,break:i.attr(s,"type")||"textWrapping"});break;case"lastRenderedPageBreak":r.children.push({type:l.Break,break:"lastRenderedPageBreak"});break;case"sym":r.children.push({type:l.Symbol,font:j(i.attr(s,"font")),char:i.attr(s,"char")});break;case"tab":r.children.push({type:l.Tab});break;case"footnoteReference":r.children.push({type:l.FootnoteReference,id:i.attr(s,"id")});break;case"endnoteReference":r.children.push({type:l.EndnoteReference,id:i.attr(s,"id")});break;case"drawing":let n=this.parseDrawing(s);n&&(r.children=[n]);break;case"pict":r.children.push(this.parseVmlPicture(s));break;case"rPr":this.parseRunProperties(s,r);break}}),r}parseMathElement(e){const t=`${e.localName}Pr`,r={type:te[e.localName],children:[]};for(const n of i.elements(e))if(te[n.localName])r.children.push(this.parseMathElement(n));else if(n.localName=="r"){var s=this.parseRun(n);s.type=l.MmlRun,r.children.push(s)}else n.localName==t&&(r.props=this.parseMathProperies(n));return r}parseMathProperies(e){const t={};for(const r of i.elements(e))switch(r.localName){case"chr":t.char=i.attr(r,"val");break;case"vertJc":t.verticalJustification=i.attr(r,"val");break;case"pos":t.position=i.attr(r,"val");break;case"degHide":t.hideDegree=i.boolAttr(r,"val");break;case"begChr":t.beginChar=i.attr(r,"val");break;case"endChr":t.endChar=i.attr(r,"val");break}return t}parseRunProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,r=>{switch(r.localName){case"rStyle":t.styleName=i.attr(r,"val");break;case"vertAlign":t.verticalAlign=f.valueOfVertAlign(r,!0);break;default:return!1}return!0})}parseVmlPicture(e){const t={type:l.VmlPicture,children:[]};for(const r of i.elements(e)){const s=ge(r,this);s&&t.children.push(s)}return t}checkAlternateContent(e){var n;if(e.localName!="AlternateContent")return e;var t=i.element(e,"Choice");if(t){var r=i.attr(t,"Requires"),s=e.lookupNamespaceURI(r);if(Ot.includes(s))return t.firstElementChild}return(n=i.element(e,"Fallback"))==null?void 0:n.firstElementChild}parseDrawing(e){for(var t of i.elements(e))switch(t.localName){case"inline":case"anchor":return this.parseDrawingWrapper(t)}}parseDrawingWrapper(e){var m;var t={type:l.Drawing,children:[],cssStyle:{}},r=e.localName=="anchor";let s=null,n=i.boolAttr(e,"simplePos");i.boolAttr(e,"behindDoc");let o={relative:"page",align:"left",offset:"0"},c={relative:"page",align:"top",offset:"0"};for(var h of i.elements(e))switch(h.localName){case"simplePos":n&&(o.offset=i.lengthAttr(h,"x",k.Emu),c.offset=i.lengthAttr(h,"y",k.Emu));break;case"extent":t.cssStyle.width=i.lengthAttr(h,"cx",k.Emu),t.cssStyle.height=i.lengthAttr(h,"cy",k.Emu);break;case"positionH":case"positionV":if(!n){let y=h.localName=="positionH"?o:c;var u=i.element(h,"align"),d=i.element(h,"posOffset");y.relative=(m=i.attr(h,"relativeFrom"))!=null?m:y.relative,u&&(y.align=u.textContent),d&&(y.offset=g.sizeValue(d,k.Emu))}break;case"wrapTopAndBottom":s="wrapTopAndBottom";break;case"wrapNone":s="wrapNone";break;case"graphic":var v=this.parseGraphic(h);v&&t.children.push(v);break}return s=="wrapTopAndBottom"?(t.cssStyle.display="block",o.align&&(t.cssStyle["text-align"]=o.align,t.cssStyle.width="100%")):s=="wrapNone"?(t.cssStyle.display="block",t.cssStyle.position="relative",t.cssStyle.width="0px",t.cssStyle.height="0px",o.offset&&(t.cssStyle.left=o.offset),c.offset&&(t.cssStyle.top=c.offset)):r&&(o.align=="left"||o.align=="right")&&(t.cssStyle.float=o.align),t}parseGraphic(e){var t=i.element(e,"graphicData");for(let r of i.elements(t))switch(r.localName){case"pic":return this.parsePicture(r)}return null}parsePicture(e){var t={type:l.Image,src:"",cssStyle:{}},r=i.element(e,"blipFill"),s=i.element(r,"blip");t.src=i.attr(s,"embed");var n=i.element(e,"spPr"),o=i.element(n,"xfrm");t.cssStyle.position="relative";for(var c of i.elements(o))switch(c.localName){case"ext":t.cssStyle.width=i.lengthAttr(c,"cx",k.Emu),t.cssStyle.height=i.lengthAttr(c,"cy",k.Emu);break;case"off":t.cssStyle.left=i.lengthAttr(c,"x",k.Emu),t.cssStyle.top=i.lengthAttr(c,"y",k.Emu);break}return t}parseTable(e){var t={type:l.Table,children:[]};return g.foreach(e,r=>{switch(r.localName){case"tr":t.children.push(this.parseTableRow(r));break;case"tblGrid":t.columns=this.parseTableColumns(r);break;case"tblPr":this.parseTableProperties(r,t);break}}),t}parseTableColumns(e){var t=[];return g.foreach(e,r=>{switch(r.localName){case"gridCol":t.push({width:i.lengthAttr(r,"w")});break}}),t}parseTableProperties(e,t){switch(t.cssStyle={},t.cellStyle={},this.parseDefaultProperties(e,t.cssStyle,t.cellStyle,r=>{switch(r.localName){case"tblStyle":t.styleName=i.attr(r,"val");break;case"tblLook":t.className=f.classNameOftblLook(r);break;case"tblpPr":this.parseTablePosition(r,t);break;case"tblStyleColBandSize":t.colBandSize=i.intAttr(r,"val");break;case"tblStyleRowBandSize":t.rowBandSize=i.intAttr(r,"val");break;default:return!1}return!0}),t.cssStyle["text-align"]){case"center":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto",t.cssStyle["margin-right"]="auto";break;case"right":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto";break}}parseTablePosition(e,t){var r=i.lengthAttr(e,"topFromText"),s=i.lengthAttr(e,"bottomFromText"),n=i.lengthAttr(e,"rightFromText"),o=i.lengthAttr(e,"leftFromText");t.cssStyle.float="left",t.cssStyle["margin-bottom"]=f.addSize(t.cssStyle["margin-bottom"],s),t.cssStyle["margin-left"]=f.addSize(t.cssStyle["margin-left"],o),t.cssStyle["margin-right"]=f.addSize(t.cssStyle["margin-right"],n),t.cssStyle["margin-top"]=f.addSize(t.cssStyle["margin-top"],r)}parseTableRow(e){var t={type:l.Row,children:[]};return g.foreach(e,r=>{switch(r.localName){case"tc":t.children.push(this.parseTableCell(r));break;case"trPr":this.parseTableRowProperties(r,t);break}}),t}parseTableRowProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,r=>{switch(r.localName){case"cnfStyle":t.className=f.classNameOfCnfStyle(r);break;case"tblHeader":t.isHeader=i.boolAttr(r,"val");break;default:return!1}return!0})}parseTableCell(e){var t={type:l.Cell,children:[]};return g.foreach(e,r=>{switch(r.localName){case"tbl":t.children.push(this.parseTable(r));break;case"p":t.children.push(this.parseParagraph(r));break;case"tcPr":this.parseTableCellProperties(r,t);break}}),t}parseTableCellProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,r=>{var s;switch(r.localName){case"gridSpan":t.span=i.intAttr(r,"val",null);break;case"vMerge":t.verticalMerge=(s=i.attr(r,"val"))!=null?s:"continue";break;case"cnfStyle":t.className=f.classNameOfCnfStyle(r);break;default:return!1}return!0}),this.parseTableCellVerticalText(e,t)}parseTableCellVerticalText(e,t){const r={btLr:{writingMode:"vertical-rl",transform:"rotate(180deg)"},lrTb:{writingMode:"vertical-lr",transform:"none"},tbRl:{writingMode:"vertical-rl",transform:"none"}};g.foreach(e,s=>{if(s.localName==="textDirection"){const n=i.attr(s,"val"),o=r[n]||{writingMode:"horizontal-tb"};t.cssStyle["writing-mode"]=o.writingMode,t.cssStyle.transform=o.transform}})}parseDefaultProperties(e,t=null,r=null,s=null){return t=t||{},g.foreach(e,n=>{if(!(s!=null&&s(n)))switch(n.localName){case"jc":t["text-align"]=f.valueOfJc(n);break;case"textAlignment":t["vertical-align"]=f.valueOfTextAlignment(n);break;case"color":t.color=g.colorAttr(n,"val",null,H.color);break;case"sz":t["font-size"]=t["min-height"]=i.lengthAttr(n,"val",k.FontSize);break;case"shd":t["background-color"]=g.colorAttr(n,"fill",null,H.shd);break;case"highlight":t["background-color"]=g.colorAttr(n,"val",null,H.highlight);break;case"vertAlign":break;case"position":t.verticalAlign=i.lengthAttr(n,"val",k.FontSize);break;case"tcW":if(this.options.ignoreWidth)break;case"tblW":t.width=f.valueOfSize(n,"w");break;case"trHeight":this.parseTrHeight(n,t);break;case"strike":t["text-decoration"]=i.boolAttr(n,"val",!0)?"line-through":"none";break;case"b":t["font-weight"]=i.boolAttr(n,"val",!0)?"bold":"normal";break;case"i":t["font-style"]=i.boolAttr(n,"val",!0)?"italic":"normal";break;case"caps":t["text-transform"]=i.boolAttr(n,"val",!0)?"uppercase":"none";break;case"smallCaps":t["font-variant"]=i.boolAttr(n,"val",!0)?"small-caps":"none";break;case"u":this.parseUnderline(n,t);break;case"ind":case"tblInd":this.parseIndentation(n,t);break;case"rFonts":this.parseFont(n,t);break;case"tblBorders":this.parseBorderProperties(n,r||t);break;case"tblCellSpacing":t["border-spacing"]=f.valueOfMargin(n),t["border-collapse"]="separate";break;case"pBdr":this.parseBorderProperties(n,t);break;case"bdr":t.border=f.valueOfBorder(n);break;case"tcBorders":this.parseBorderProperties(n,t);break;case"vanish":i.boolAttr(n,"val",!0)&&(t.display="none");break;case"kern":break;case"noWrap":break;case"tblCellMar":case"tcMar":this.parseMarginProperties(n,r||t);break;case"tblLayout":t["table-layout"]=f.valueOfTblLayout(n);break;case"vAlign":t["vertical-align"]=f.valueOfTextAlignment(n);break;case"spacing":e.localName=="pPr"&&this.parseSpacing(n,t);break;case"wordWrap":i.boolAttr(n,"val")&&(t["overflow-wrap"]="break-word");break;case"suppressAutoHyphens":t.hyphens=i.boolAttr(n,"val",!0)?"none":"auto";break;case"lang":t.$lang=i.attr(n,"val");break;case"bCs":case"iCs":case"szCs":case"tabs":case"outlineLvl":case"contextualSpacing":case"tblStyleColBandSize":case"tblStyleRowBandSize":case"webHidden":case"pageBreakBefore":case"suppressLineNumbers":case"keepLines":case"keepNext":case"widowControl":case"bidi":case"rtl":case"noProof":break;default:this.options.debug;break}}),t}parseUnderline(e,t){var r=i.attr(e,"val");if(r!=null){switch(r){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":t["text-decoration"]="underline dashed";break;case"dotted":case"dottedHeavy":t["text-decoration"]="underline dotted";break;case"double":t["text-decoration"]="underline double";break;case"single":case"thick":t["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":t["text-decoration"]="underline wavy";break;case"words":t["text-decoration"]="underline";break;case"none":t["text-decoration"]="none";break}var s=g.colorAttr(e,"color");s&&(t["text-decoration-color"]=s)}}parseFont(e,t){var r=i.attr(e,"ascii"),s=f.themeValue(e,"asciiTheme"),n=i.attr(e,"eastAsia"),o=[r,s,n].filter(c=>c).map(c=>j(c));o.length>0&&(t["font-family"]=[...new Set(o)].join(", "))}parseIndentation(e,t){var r=i.lengthAttr(e,"firstLine"),s=i.lengthAttr(e,"hanging"),n=i.lengthAttr(e,"left"),o=i.lengthAttr(e,"start"),c=i.lengthAttr(e,"right"),h=i.lengthAttr(e,"end");r&&(t["text-indent"]=r),s&&(t["text-indent"]=`-${s}`),(n||o)&&(t["margin-left"]=n||o),(c||h)&&(t["margin-right"]=c||h)}parseSpacing(e,t){var r=i.lengthAttr(e,"before"),s=i.lengthAttr(e,"after"),n=i.intAttr(e,"line",null),o=i.attr(e,"lineRule");if(r&&(t["margin-top"]=r),s&&(t["margin-bottom"]=s),n!==null)switch(o){case"auto":t["line-height"]=`${(n/240).toFixed(2)}`;break;case"atLeast":t["line-height"]=`calc(100% + ${n/20}pt)`;break;default:t["line-height"]=t["min-height"]=`${n/20}pt`;break}}parseMarginProperties(e,t){g.foreach(e,r=>{switch(r.localName){case"left":t["padding-left"]=f.valueOfMargin(r);break;case"right":t["padding-right"]=f.valueOfMargin(r);break;case"top":t["padding-top"]=f.valueOfMargin(r);break;case"bottom":t["padding-bottom"]=f.valueOfMargin(r);break}})}parseTrHeight(e,t){switch(i.attr(e,"hRule")){case"exact":t.height=i.lengthAttr(e,"val");break;case"atLeast":default:t.height=i.lengthAttr(e,"val");break}}parseBorderProperties(e,t){g.foreach(e,r=>{switch(r.localName){case"start":case"left":t["border-left"]=f.valueOfBorder(r);break;case"end":case"right":t["border-right"]=f.valueOfBorder(r);break;case"top":t["border-top"]=f.valueOfBorder(r);break;case"bottom":t["border-bottom"]=f.valueOfBorder(r);break}})}}const It=["black","blue","cyan","darkBlue","darkCyan","darkGray","darkGreen","darkMagenta","darkRed","darkYellow","green","lightGray","magenta","none","red","white","yellow"];class g{static foreach(e,t){for(var r=0;r<e.childNodes.length;r++){let s=e.childNodes[r];s.nodeType==Node.ELEMENT_NODE&&t(s)}}static colorAttr(e,t,r=null,s="black"){var n=i.attr(e,t);if(n)return n=="auto"?s:It.includes(n)?n:`#${n}`;var o=i.attr(e,"themeColor");return o?`var(--docx-${o}-color)`:r}static sizeValue(e,t=k.Dxa){return ie(e.textContent,t)}}class f{static themeValue(e,t){var r=i.attr(e,t);return r?`var(--docx-${r}-font)`:null}static valueOfSize(e,t){var r=k.Dxa;switch(i.attr(e,"type")){case"dxa":break;case"pct":r=k.Percent;break;case"auto":return"auto"}return i.lengthAttr(e,t,r)}static valueOfMargin(e){return i.lengthAttr(e,"w")}static valueOfBorder(e){var t=i.attr(e,"val");if(t=="nil")return"none";var r=g.colorAttr(e,"color"),s=i.lengthAttr(e,"sz",k.Border);return`${s} solid ${r=="auto"?H.borderColor:r}`}static valueOfTblLayout(e){var t=i.attr(e,"val");return t=="fixed"?"fixed":"auto"}static classNameOfCnfStyle(e){const t=i.attr(e,"val");return["first-row","last-row","first-col","last-col","odd-col","even-col","odd-row","even-row","ne-cell","nw-cell","se-cell","sw-cell"].filter((s,n)=>t[n]=="1").join(" ")}static valueOfJc(e){var t=i.attr(e,"val");switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":return"justify"}return t}static valueOfVertAlign(e,t=!1){var r=i.attr(e,"val");switch(r){case"subscript":return"sub";case"superscript":return t?"sup":"super"}return t?null:r}static valueOfTextAlignment(e){var t=i.attr(e,"val");switch(t){case"auto":case"baseline":return"baseline";case"top":return"top";case"center":return"middle";case"bottom":return"bottom"}return t}static addSize(e,t){return e==null?t:t==null?e:`calc(${e} + ${t})`}static classNameOftblLook(e){const t=i.hexAttr(e,"val",0);let r="";return(i.boolAttr(e,"firstRow")||t&32)&&(r+=" first-row"),(i.boolAttr(e,"lastRow")||t&64)&&(r+=" last-row"),(i.boolAttr(e,"firstColumn")||t&128)&&(r+=" first-col"),(i.boolAttr(e,"lastColumn")||t&256)&&(r+=" last-col"),(i.boolAttr(e,"noHBand")||t&512)&&(r+=" no-hband"),(i.boolAttr(e,"noVBand")||t&1024)&&(r+=" no-vband"),r.trim()}}const re={pos:0,leader:"none",style:"left"},_t=50;function zt(a=document.body){const e=document.createElement("div");e.style.width="100pt",a.appendChild(e);const t=100/e.offsetWidth;return a.removeChild(e),t}function Vt(a,e,t,r=72/96){const s=a.closest("p"),n=a.getBoundingClientRect(),o=s.getBoundingClientRect(),c=getComputedStyle(s),h=(e==null?void 0:e.length)>0?e.map(N=>({pos:ae(N.position),leader:N.leader,style:N.style})).sort((N,T)=>N.pos-T.pos):[re],u=h[h.length-1],d=o.width*r,v=ae(t);let m=u.pos+v;if(m<d)for(;m<d&&h.length<_t;m+=v)h.push(B(P({},re),{pos:m}));const y=parseFloat(c.marginLeft),M=o.left+y,E=(n.left-M)*r,S=h.find(N=>N.style!="clear"&&N.pos>E);if(S==null)return;let R=1;if(S.style=="right"||S.style=="center"){const N=Array.from(s.querySelectorAll(`.${a.className}`)),T=N.indexOf(a)+1,$=document.createRange();$.setStart(a,1),T<N.length?$.setEndBefore(N[T]):$.setEndAfter(s);const ke=S.style=="center"?.5:1,G=$.getBoundingClientRect(),ve=G.left+ke*G.width-(o.left-y);R=S.pos-ve*r}else R=S.pos-E;switch(a.innerHTML="&nbsp;",a.style.textDecoration="inherit",a.style.wordSpacing=`${R.toFixed(0)}pt`,S.leader){case"dot":case"middleDot":a.style.textDecoration="underline",a.style.textDecorationStyle="dotted";break;case"hyphen":case"heavy":case"underscore":a.style.textDecoration="underline";break}}function ae(a){return parseFloat(a)}const p={svg:"http://www.w3.org/2000/svg",mathML:"http://www.w3.org/1998/Math/MathML"};class jt{constructor(e){this.htmlDocument=e,this.className="docx",this.styleMap={},this.currentPart=null,this.tableVerticalMerges=[],this.currentVerticalMerge=null,this.tableCellPositions=[],this.currentCellPosition=null,this.footnoteMap={},this.endnoteMap={},this.currentEndnoteIds=[],this.usedHederFooterParts=[],this.currentTabs=[],this.commentMap={},this.tasks=[],this.postRenderTasks=[]}render(e,t,r=null,s){return w(this,null,function*(){var o;this.document=e,this.options=s,this.className=s.className,this.rootSelector=s.inWrapper?`.${this.className}-wrapper`:":root",this.styleMap=null,this.tasks=[],this.options.renderComments&&globalThis.Highlight&&(this.commentHighlight=new Highlight),r=r||t,se(r),se(t),r.appendChild(this.createComment("docxjs library predefined styles")),r.appendChild(this.renderDefaultStyle()),e.themePart&&(r.appendChild(this.createComment("docxjs document theme values")),this.renderTheme(e.themePart,r)),e.stylesPart!=null&&(this.styleMap=this.processStyles(e.stylesPart.styles),r.appendChild(this.createComment("docxjs document styles")),r.appendChild(this.renderStyles(e.stylesPart.styles))),e.numberingPart&&(this.prodessNumberings(e.numberingPart.domNumberings),r.appendChild(this.createComment("docxjs document numbering styles")),r.appendChild(this.renderNumbering(e.numberingPart.domNumberings,r))),e.footnotesPart&&(this.footnoteMap=A(e.footnotesPart.notes,c=>c.id)),e.endnotesPart&&(this.endnoteMap=A(e.endnotesPart.notes,c=>c.id)),e.settingsPart&&(this.defaultTabSize=(o=e.settingsPart.settings)==null?void 0:o.defaultTabStop),!s.ignoreFonts&&e.fontTablePart&&this.renderFontTable(e.fontTablePart,r);var n=this.renderSections(e.documentPart.body);this.options.inWrapper?t.appendChild(this.renderWrapper(n)):V(t,n),this.commentHighlight&&s.renderComments&&CSS.highlights.set(`${this.className}-comments`,this.commentHighlight),this.postRenderTasks.forEach(c=>c()),yield Promise.allSettled(this.tasks),this.refreshTabStops()})}renderTheme(e,t){var c,h;const r={},s=(c=e.theme)==null?void 0:c.fontScheme;s&&(s.majorFont&&(r["--docx-majorHAnsi-font"]=s.majorFont.latinTypeface),s.minorFont&&(r["--docx-minorHAnsi-font"]=s.minorFont.latinTypeface));const n=(h=e.theme)==null?void 0:h.colorScheme;if(n)for(let[u,d]of Object.entries(n.colors))r[`--docx-${u}-color`]=`#${d}`;const o=this.styleToString(`.${this.className}`,r);t.appendChild(this.createStyleElement(o))}renderFontTable(e,t){for(let r of e.fonts)for(let s of r.embedFontRefs)this.tasks.push(this.document.loadFont(s.id,s.key).then(n=>{const o={"font-family":j(r.name),src:`url(${n})`};(s.type=="bold"||s.type=="boldItalic")&&(o["font-weight"]="bold"),(s.type=="italic"||s.type=="boldItalic")&&(o["font-style"]="italic");const c=this.styleToString("@font-face",o);t.appendChild(this.createComment(`docxjs ${r.name} font`)),t.appendChild(this.createStyleElement(c))}))}processStyleName(e){return e?`${this.className}_${Ee(e)}`:this.className}processStyles(e){const t=A(e.filter(s=>s.id!=null),s=>s.id);for(const s of e.filter(n=>n.basedOn)){var r=t[s.basedOn];if(r){s.paragraphProps=I(s.paragraphProps,r.paragraphProps),s.runProps=I(s.runProps,r.runProps);for(const n of r.styles){const o=s.styles.find(c=>c.target==n.target);o?this.copyStyleProperties(n.values,o.values):s.styles.push(B(P({},n),{values:P({},n.values)}))}}else this.options.debug}for(let s of e)s.cssName=this.processStyleName(s.id);return t}prodessNumberings(e){var t;for(let r of e.filter(s=>s.pStyleName)){const s=this.findStyle(r.pStyleName);(t=s==null?void 0:s.paragraphProps)!=null&&t.numbering&&(s.paragraphProps.numbering.level=r.level)}}processElement(e){if(e.children)for(var t of e.children)t.parent=e,t.type==l.Table?this.processTable(t):this.processElement(t)}processTable(e){for(var t of e.children)for(var r of t.children)r.cssStyle=this.copyStyleProperties(e.cellStyle,r.cssStyle,["border-left","border-right","border-top","border-bottom","padding-left","padding-right","padding-top","padding-bottom"]),this.processElement(r)}copyStyleProperties(e,t,r=null){if(!e)return t;t==null&&(t={}),r==null&&(r=Object.getOwnPropertyNames(e));for(var s of r)e.hasOwnProperty(s)&&!t.hasOwnProperty(s)&&(t[s]=e[s]);return t}createPageElement(e,t){var r=this.createElement("section",{className:e});return t&&(t.pageMargins&&(r.style.paddingLeft=t.pageMargins.left,r.style.paddingRight=t.pageMargins.right,r.style.paddingTop=t.pageMargins.top,r.style.paddingBottom=t.pageMargins.bottom),t.pageSize&&(this.options.ignoreWidth||(r.style.width=t.pageSize.width),this.options.ignoreHeight||(r.style.minHeight=t.pageSize.height))),r}createSectionContent(e){var t=this.createElement("article");return e.columns&&e.columns.numberOfColumns&&(t.style.columnCount=`${e.columns.numberOfColumns}`,t.style.columnGap=e.columns.space,e.columns.separator&&(t.style.columnRule="1px solid black")),t}renderSections(e){const t=[];this.processElement(e);const r=this.splitBySection(e.children,e.props),s=this.groupByPageBreaks(r);let n=null;for(let c=0,h=s.length;c<h;c++){this.currentFootnoteIds=[];let d=s[c][0].sectProps;const v=this.createPageElement(this.className,d);this.renderStyleValues(e.cssStyle,v),this.options.renderHeaders&&this.renderHeaderFooter(d.headerRefs,d,t.length,n!=d,v);for(const m of s[c]){var o=this.createSectionContent(m.sectProps);this.renderElements(m.elements,o),v.appendChild(o),d=m.sectProps}this.options.renderFootnotes&&this.renderNotes(this.currentFootnoteIds,this.footnoteMap,v),this.options.renderEndnotes&&c==h-1&&this.renderNotes(this.currentEndnoteIds,this.endnoteMap,v),this.options.renderFooters&&this.renderHeaderFooter(d.footerRefs,d,t.length,n!=d,v),t.push(v),n=d}return t}renderHeaderFooter(e,t,r,s,n){var h,u;if(e){var o=(u=(h=t.titlePage&&s?e.find(d=>d.type=="first"):null)!=null?h:r%2==1?e.find(d=>d.type=="even"):null)!=null?u:e.find(d=>d.type=="default"),c=o&&this.document.findPartByRelId(o.id,this.document.documentPart);if(c){this.currentPart=c,this.usedHederFooterParts.includes(c.path)||(this.processElement(c.rootElement),this.usedHederFooterParts.push(c.path));const[d]=this.renderElements([c.rootElement],n);t!=null&&t.pageMargins&&(c.rootElement.type===l.Header?(d.style.marginTop=`calc(${t.pageMargins.header} - ${t.pageMargins.top})`,d.style.minHeight=`calc(${t.pageMargins.top} - ${t.pageMargins.header})`):c.rootElement.type===l.Footer&&(d.style.marginBottom=`calc(${t.pageMargins.footer} - ${t.pageMargins.bottom})`,d.style.minHeight=`calc(${t.pageMargins.bottom} - ${t.pageMargins.footer})`)),this.currentPart=null}}}isPageBreakElement(e){return e.type!=l.Break?!1:e.break=="lastRenderedPageBreak"?!this.options.ignoreLastRenderedPageBreak:e.break=="page"}isPageBreakSection(e,t){var r,s,n,o,c,h;return!e||!t?!1:((r=e.pageSize)==null?void 0:r.orientation)!=((s=t.pageSize)==null?void 0:s.orientation)||((n=e.pageSize)==null?void 0:n.width)!=((o=t.pageSize)==null?void 0:o.width)||((c=e.pageSize)==null?void 0:c.height)!=((h=t.pageSize)==null?void 0:h.height)}splitBySection(e,t){var v;var r={sectProps:null,elements:[],pageBreak:!1},s=[r];for(let m of e){if(m.type==l.Paragraph){const y=this.findStyle(m.styleName);(v=y==null?void 0:y.paragraphProps)!=null&&v.pageBreakBefore&&(r.sectProps=n,r.pageBreak=!0,r={sectProps:null,elements:[],pageBreak:!1},s.push(r))}if(r.elements.push(m),m.type==l.Paragraph){const y=m;var n=y.sectionProps,o=-1,c=-1;if(this.options.breakPages&&y.children&&(o=y.children.findIndex(M=>{var E,S;return c=(S=(E=M.children)==null?void 0:E.findIndex(this.isPageBreakElement.bind(this)))!=null?S:-1,c!=-1})),(n||o!=-1)&&(r.sectProps=n,r.pageBreak=o!=-1,r={sectProps:null,elements:[],pageBreak:!1},s.push(r)),o!=-1){let M=y.children[o],E=c<M.children.length-1;if(o<y.children.length-1||E){var h=m.children,u=B(P({},m),{children:h.slice(o)});if(m.children=h.slice(0,o),r.elements.push(u),E){let S=M.children,R=B(P({},M),{children:S.slice(0,c)});m.children.push(R),M.children=S.slice(c)}}}}}let d=null;for(let m=s.length-1;m>=0;m--)s[m].sectProps==null?s[m].sectProps=d!=null?d:t:d=s[m].sectProps;return s}groupByPageBreaks(e){let t=[],r;const s=[t];for(let n of e)t.push(n),(this.options.ignoreLastRenderedPageBreak||n.pageBreak||this.isPageBreakSection(r,n.sectProps))&&s.push(t=[]),r=n.sectProps;return s.filter(n=>n.length>0)}renderWrapper(e){return this.createElement("div",{className:`${this.className}-wrapper`},e)}renderDefaultStyle(){var e=this.className,t=`
.${e}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.${e}-wrapper>section.${e} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }`;this.options.hideWrapperOnPrint&&(t=`@media not print { ${t} }`);var r=`${t}
.${e} { color: black; hyphens: auto; text-underline-position: from-font; }
section.${e} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.${e}>article { margin-bottom: auto; z-index: 1; }
section.${e}>footer { z-index: 1; }
.${e} table { border-collapse: collapse; }
.${e} table td, .${e} table th { vertical-align: top; }
.${e} p { margin: 0pt; min-height: 1em; }
.${e} span { white-space: pre-wrap; overflow-wrap: break-word; }
.${e} a { color: inherit; text-decoration: inherit; }
.${e} svg { fill: transparent; }
`;return this.options.renderComments&&(r+=`
.${e}-comment-ref { cursor: default; }
.${e}-comment-popover { display: none; z-index: 1000; padding: 0.5rem; background: white; position: absolute; box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.25); width: 30ch; }
.${e}-comment-ref:hover~.${e}-comment-popover { display: block; }
.${e}-comment-author,.${e}-comment-date { font-size: 0.875rem; color: #888; }
`),this.createStyleElement(r)}renderNumbering(e,t){var r="",s=[];for(var n of e){var o=`p.${this.numberingClass(n.id,n.level)}`,c="none";if(n.bullet){let h=`--${this.className}-${n.bullet.src}`.toLowerCase();r+=this.styleToString(`${o}:before`,{content:"' '",display:"inline-block",background:`var(${h})`},n.bullet.style),this.tasks.push(this.document.loadNumberingImage(n.bullet.src).then(u=>{var d=`${this.rootSelector} { ${h}: url(${u}) }`;t.appendChild(this.createStyleElement(d))}))}else if(n.levelText){let h=this.numberingCounter(n.id,n.level);const u=h+" "+(n.start-1);n.level>0&&(r+=this.styleToString(`p.${this.numberingClass(n.id,n.level-1)}`,{"counter-set":u})),s.push(u),r+=this.styleToString(`${o}:before`,P({content:this.levelTextToContent(n.levelText,n.suff,n.id,this.numFormatToCssValue(n.format)),"counter-increment":h},n.rStyle))}else c=this.numFormatToCssValue(n.format);r+=this.styleToString(o,P({display:"list-item","list-style-position":"inside","list-style-type":c},n.pStyle))}return s.length>0&&(r+=this.styleToString(this.rootSelector,{"counter-reset":s.join(" ")})),this.createStyleElement(r)}renderStyles(e){var h;var t="";const r=this.styleMap,s=A(e.filter(u=>u.isDefault),u=>u.target);for(const u of e){var n=u.styles;if(u.linked){var o=u.linked&&r[u.linked];o?n=n.concat(o.styles):this.options.debug}for(const d of n){var c=`${(h=u.target)!=null?h:""}.${u.cssName}`;u.target!=d.target&&(c+=` ${d.target}`),s[u.target]==u&&(c=`.${this.className} ${u.target}, `+c),t+=this.styleToString(c,d.values)}}return this.createStyleElement(t)}renderNotes(e,t,r){var s=e.map(o=>t[o]).filter(o=>o);if(s.length>0){var n=this.createElement("ol",null,this.renderElements(s));r.appendChild(n)}}renderElement(e){switch(e.type){case l.Paragraph:return this.renderParagraph(e);case l.BookmarkStart:return this.renderBookmarkStart(e);case l.BookmarkEnd:return null;case l.Run:return this.renderRun(e);case l.Table:return this.renderTable(e);case l.Row:return this.renderTableRow(e);case l.Cell:return this.renderTableCell(e);case l.Hyperlink:return this.renderHyperlink(e);case l.SmartTag:return this.renderSmartTag(e);case l.Drawing:return this.renderDrawing(e);case l.Image:return this.renderImage(e);case l.Text:return this.renderText(e);case l.Text:return this.renderText(e);case l.DeletedText:return this.renderDeletedText(e);case l.Tab:return this.renderTab(e);case l.Symbol:return this.renderSymbol(e);case l.Break:return this.renderBreak(e);case l.Footer:return this.renderContainer(e,"footer");case l.Header:return this.renderContainer(e,"header");case l.Footnote:case l.Endnote:return this.renderContainer(e,"li");case l.FootnoteReference:return this.renderFootnoteReference(e);case l.EndnoteReference:return this.renderEndnoteReference(e);case l.NoBreakHyphen:return this.createElement("wbr");case l.VmlPicture:return this.renderVmlPicture(e);case l.VmlElement:return this.renderVmlElement(e);case l.MmlMath:return this.renderContainerNS(e,p.mathML,"math",{xmlns:p.mathML});case l.MmlMathParagraph:return this.renderContainer(e,"span");case l.MmlFraction:return this.renderContainerNS(e,p.mathML,"mfrac");case l.MmlBase:return this.renderContainerNS(e,p.mathML,e.parent.type==l.MmlMatrixRow?"mtd":"mrow");case l.MmlNumerator:case l.MmlDenominator:case l.MmlFunction:case l.MmlLimit:case l.MmlBox:return this.renderContainerNS(e,p.mathML,"mrow");case l.MmlGroupChar:return this.renderMmlGroupChar(e);case l.MmlLimitLower:return this.renderContainerNS(e,p.mathML,"munder");case l.MmlMatrix:return this.renderContainerNS(e,p.mathML,"mtable");case l.MmlMatrixRow:return this.renderContainerNS(e,p.mathML,"mtr");case l.MmlRadical:return this.renderMmlRadical(e);case l.MmlSuperscript:return this.renderContainerNS(e,p.mathML,"msup");case l.MmlSubscript:return this.renderContainerNS(e,p.mathML,"msub");case l.MmlDegree:case l.MmlSuperArgument:case l.MmlSubArgument:return this.renderContainerNS(e,p.mathML,"mn");case l.MmlFunctionName:return this.renderContainerNS(e,p.mathML,"ms");case l.MmlDelimiter:return this.renderMmlDelimiter(e);case l.MmlRun:return this.renderMmlRun(e);case l.MmlNary:return this.renderMmlNary(e);case l.MmlPreSubSuper:return this.renderMmlPreSubSuper(e);case l.MmlBar:return this.renderMmlBar(e);case l.MmlEquationArray:return this.renderMllList(e);case l.Inserted:return this.renderInserted(e);case l.Deleted:return this.renderDeleted(e);case l.CommentRangeStart:return this.renderCommentRangeStart(e);case l.CommentRangeEnd:return this.renderCommentRangeEnd(e);case l.CommentReference:return this.renderCommentReference(e);case l.AltChunk:return this.renderAltChunk(e)}return null}renderElements(e,t){if(e==null)return null;var r=e.flatMap(s=>this.renderElement(s)).filter(s=>s!=null);return t&&V(t,r),r}renderContainer(e,t,r){return this.createElement(t,r,this.renderElements(e.children))}renderContainerNS(e,t,r,s){return this.createElementNS(t,r,s,this.renderElements(e.children))}renderParagraph(e){var n,o,c,h;var t=this.renderContainer(e,"p");const r=this.findStyle(e.styleName);(o=e.tabs)!=null||(e.tabs=(n=r==null?void 0:r.paragraphProps)==null?void 0:n.tabs),this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.renderCommonProperties(t.style,e);const s=(h=e.numbering)!=null?h:(c=r==null?void 0:r.paragraphProps)==null?void 0:c.numbering;return s&&t.classList.add(this.numberingClass(s.id,s.level)),t}renderRunProperties(e,t){this.renderCommonProperties(e,t)}renderCommonProperties(e,t){t!=null&&(t.color&&(e.color=t.color),t.fontSize&&(e["font-size"]=t.fontSize))}renderHyperlink(e){var s;var t=this.renderContainer(e,"a");this.renderStyleValues(e.cssStyle,t);let r="";if(e.id){const n=this.document.documentPart.rels.find(o=>o.id==e.id&&o.targetMode==="External");r=(s=n==null?void 0:n.target)!=null?s:r}return e.anchor&&(r+=`#${e.anchor}`),t.href=r,t}renderSmartTag(e){return this.renderContainer(e,"span")}renderCommentRangeStart(e){var s;if(!this.options.renderComments)return null;const t=new Range;(s=this.commentHighlight)==null||s.add(t);const r=this.htmlDocument.createComment(`start of comment #${e.id}`);return this.later(()=>t.setStart(r,0)),this.commentMap[e.id]=t,r}renderCommentRangeEnd(e){if(!this.options.renderComments)return null;const t=this.commentMap[e.id],r=this.htmlDocument.createComment(`end of comment #${e.id}`);return this.later(()=>t==null?void 0:t.setEnd(r,0)),r}renderCommentReference(e){var o;if(!this.options.renderComments)return null;var t=(o=this.document.commentsPart)==null?void 0:o.commentMap[e.id];if(!t)return null;const r=new DocumentFragment,s=this.createElement("span",{className:`${this.className}-comment-ref`},["💬"]),n=this.createElement("div",{className:`${this.className}-comment-popover`});return this.renderCommentContent(t,n),r.appendChild(this.htmlDocument.createComment(`comment #${t.id} by ${t.author} on ${t.date}`)),r.appendChild(s),r.appendChild(n),r}renderAltChunk(e){if(!this.options.renderAltChunks)return null;var t=this.createElement("iframe");return this.tasks.push(this.document.loadAltChunk(e.id,this.currentPart).then(r=>{t.srcdoc=r})),t}renderCommentContent(e,t){t.appendChild(this.createElement("div",{className:`${this.className}-comment-author`},[e.author])),t.appendChild(this.createElement("div",{className:`${this.className}-comment-date`},[new Date(e.date).toLocaleString()])),this.renderElements(e.children,t)}renderDrawing(e){var t=this.renderContainer(e,"div");return t.style.display="inline-block",t.style.position="relative",t.style.textIndent="0px",this.renderStyleValues(e.cssStyle,t),t}renderImage(e){let t=this.createElement("img");return this.renderStyleValues(e.cssStyle,t),this.document&&this.tasks.push(this.document.loadDocumentImage(e.src,this.currentPart).then(r=>{t.src=r})),t}renderText(e){return this.htmlDocument.createTextNode(e.text)}renderDeletedText(e){return this.options.renderEndnotes?this.htmlDocument.createTextNode(e.text):null}renderBreak(e){return e.break=="textWrapping"?this.createElement("br"):null}renderInserted(e){return this.options.renderChanges?this.renderContainer(e,"ins"):this.renderElements(e.children)}renderDeleted(e){return this.options.renderChanges?this.renderContainer(e,"del"):null}renderSymbol(e){var t=this.createElement("span");return t.style.fontFamily=e.font,t.innerHTML=`&#x${e.char};`,t}renderFootnoteReference(e){var t=this.createElement("sup");return this.currentFootnoteIds.push(e.id),t.textContent=`${this.currentFootnoteIds.length}`,t}renderEndnoteReference(e){var t=this.createElement("sup");return this.currentEndnoteIds.push(e.id),t.textContent=`${this.currentEndnoteIds.length}`,t}renderTab(e){var s;var t=this.createElement("span");if(t.innerHTML="&emsp;",this.options.experimental){t.className=this.tabStopClass();var r=(s=Wt(e,l.Paragraph))==null?void 0:s.tabs;this.currentTabs.push({stops:r,span:t})}return t}renderBookmarkStart(e){return this.createElement("span",{id:e.name})}renderRun(e){if(e.fieldRun)return null;const t=this.createElement("span");if(e.id&&(t.id=e.id),this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.verticalAlign){const r=this.createElement(e.verticalAlign);this.renderElements(e.children,r),t.appendChild(r)}else this.renderElements(e.children,t);return t}renderTable(e){let t=this.createElement("table");return this.tableCellPositions.push(this.currentCellPosition),this.tableVerticalMerges.push(this.currentVerticalMerge),this.currentVerticalMerge={},this.currentCellPosition={col:0,row:0},e.columns&&t.appendChild(this.renderTableColumns(e.columns)),this.renderClass(e,t),this.renderElements(e.children,t),this.renderStyleValues(e.cssStyle,t),this.currentVerticalMerge=this.tableVerticalMerges.pop(),this.currentCellPosition=this.tableCellPositions.pop(),t}renderTableColumns(e){let t=this.createElement("colgroup");for(let r of e){let s=this.createElement("col");r.width&&(s.style.width=r.width),t.appendChild(s)}return t}renderTableRow(e){let t=this.renderContainer(e,"tr");return this.currentCellPosition.col=0,this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.currentCellPosition.row++,t}renderTableCell(e){let t=this.renderContainer(e,"td");const r=this.currentCellPosition.col;return e.verticalMerge?e.verticalMerge=="restart"?(this.currentVerticalMerge[r]=t,t.rowSpan=1):this.currentVerticalMerge[r]&&(this.currentVerticalMerge[r].rowSpan+=1,t.style.display="none"):this.currentVerticalMerge[r]=null,this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.span&&(t.colSpan=e.span),this.currentCellPosition.col+=t.colSpan,t}renderVmlPicture(e){return this.renderContainer(e,"div")}renderVmlElement(e){var s,n;var t=this.createSvgElement("svg");t.setAttribute("style",e.cssStyleText);const r=this.renderVmlChildElement(e);return(s=e.imageHref)!=null&&s.id&&this.tasks.push((n=this.document)==null?void 0:n.loadDocumentImage(e.imageHref.id,this.currentPart).then(o=>r.setAttribute("href",o))),t.appendChild(r),requestAnimationFrame(()=>{const o=t.firstElementChild.getBBox();t.setAttribute("width",`${Math.ceil(o.x+o.width)}`),t.setAttribute("height",`${Math.ceil(o.y+o.height)}`)}),t}renderVmlChildElement(e){const t=this.createSvgElement(e.tagName);Object.entries(e.attrs).forEach(([r,s])=>t.setAttribute(r,s));for(let r of e.children)r.type==l.VmlElement?t.appendChild(this.renderVmlChildElement(r)):t.appendChild(...F(this.renderElement(r)));return t}renderMmlRadical(e){var s;const t=e.children.find(n=>n.type==l.MmlBase);if((s=e.props)!=null&&s.hideDegree)return this.createElementNS(p.mathML,"msqrt",null,this.renderElements([t]));const r=e.children.find(n=>n.type==l.MmlDegree);return this.createElementNS(p.mathML,"mroot",null,this.renderElements([t,r]))}renderMmlDelimiter(e){var r,s;const t=[];return t.push(this.createElementNS(p.mathML,"mo",null,[(r=e.props.beginChar)!=null?r:"("])),t.push(...this.renderElements(e.children)),t.push(this.createElementNS(p.mathML,"mo",null,[(s=e.props.endChar)!=null?s:")"])),this.createElementNS(p.mathML,"mrow",null,t)}renderMmlNary(e){var u,d;const t=[],r=A(e.children,v=>v.type),s=r[l.MmlSuperArgument],n=r[l.MmlSubArgument],o=s?this.createElementNS(p.mathML,"mo",null,F(this.renderElement(s))):null,c=n?this.createElementNS(p.mathML,"mo",null,F(this.renderElement(n))):null,h=this.createElementNS(p.mathML,"mo",null,[(d=(u=e.props)==null?void 0:u.char)!=null?d:"∫"]);return o||c?t.push(this.createElementNS(p.mathML,"munderover",null,[h,c,o])):o?t.push(this.createElementNS(p.mathML,"mover",null,[h,o])):c?t.push(this.createElementNS(p.mathML,"munder",null,[h,c])):t.push(h),t.push(...this.renderElements(r[l.MmlBase].children)),this.createElementNS(p.mathML,"mrow",null,t)}renderMmlPreSubSuper(e){const t=[],r=A(e.children,u=>u.type),s=r[l.MmlSuperArgument],n=r[l.MmlSubArgument],o=s?this.createElementNS(p.mathML,"mo",null,F(this.renderElement(s))):null,c=n?this.createElementNS(p.mathML,"mo",null,F(this.renderElement(n))):null,h=this.createElementNS(p.mathML,"mo",null);return t.push(this.createElementNS(p.mathML,"msubsup",null,[h,c,o])),t.push(...this.renderElements(r[l.MmlBase].children)),this.createElementNS(p.mathML,"mrow",null,t)}renderMmlGroupChar(e){const t=e.props.verticalJustification==="bot"?"mover":"munder",r=this.renderContainerNS(e,p.mathML,t);return e.props.char&&r.appendChild(this.createElementNS(p.mathML,"mo",null,[e.props.char])),r}renderMmlBar(e){const t=this.renderContainerNS(e,p.mathML,"mrow");switch(e.props.position){case"top":t.style.textDecoration="overline";break;case"bottom":t.style.textDecoration="underline";break}return t}renderMmlRun(e){const t=this.createElementNS(p.mathML,"ms",null,this.renderElements(e.children));return this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),t}renderMllList(e){const t=this.createElementNS(p.mathML,"mtable");this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t);for(let r of this.renderElements(e.children))t.appendChild(this.createElementNS(p.mathML,"mtr",null,[this.createElementNS(p.mathML,"mtd",null,[r])]));return t}renderStyleValues(e,t){for(let r in e)r.startsWith("$")?t.setAttribute(r.slice(1),e[r]):t.style[r]=e[r]}renderClass(e,t){e.className&&(t.className=e.className),e.styleName&&t.classList.add(this.processStyleName(e.styleName))}findStyle(e){var t;return e&&((t=this.styleMap)==null?void 0:t[e])}numberingClass(e,t){return`${this.className}-num-${e}-${t}`}tabStopClass(){return`${this.className}-tab-stop`}styleToString(e,t,r=null){let s=`${e} {\r
`;for(const n in t)n.startsWith("$")||(s+=`  ${n}: ${t[n]};\r
`);return r&&(s+=r),s+`}\r
`}numberingCounter(e,t){return`${this.className}-num-${e}-${t}`}levelTextToContent(e,t,r,s){var c;const n={tab:"\\9",space:"\\a0"};var o=e.replace(/%\d*/g,h=>{let u=parseInt(h.substring(1),10)-1;return`"counter(${this.numberingCounter(r,u)}, ${s})"`});return`"${o}${(c=n[t])!=null?c:""}"`}numFormatToCssValue(e){var r;var t={none:"none",bullet:"disc",decimal:"decimal",lowerLetter:"lower-alpha",upperLetter:"upper-alpha",lowerRoman:"lower-roman",upperRoman:"upper-roman",decimalZero:"decimal-leading-zero",aiueo:"katakana",aiueoFullWidth:"katakana",chineseCounting:"simp-chinese-informal",chineseCountingThousand:"simp-chinese-informal",chineseLegalSimplified:"simp-chinese-formal",chosung:"hangul-consonant",ideographDigital:"cjk-ideographic",ideographTraditional:"cjk-heavenly-stem",ideographLegalTraditional:"trad-chinese-formal",ideographZodiac:"cjk-earthly-branch",iroha:"katakana-iroha",irohaFullWidth:"katakana-iroha",japaneseCounting:"japanese-informal",japaneseDigitalTenThousand:"cjk-decimal",japaneseLegal:"japanese-formal",thaiNumbers:"thai",koreanCounting:"korean-hangul-formal",koreanDigital:"korean-hangul-formal",koreanDigital2:"korean-hanja-informal",hebrew1:"hebrew",hebrew2:"hebrew",hindiNumbers:"devanagari",ganada:"hangul",taiwaneseCounting:"cjk-ideographic",taiwaneseCountingThousand:"cjk-ideographic",taiwaneseDigital:"cjk-decimal"};return(r=t[e])!=null?r:e}refreshTabStops(){this.options.experimental&&setTimeout(()=>{const e=zt();for(let t of this.currentTabs)Vt(t.span,t.stops,this.defaultTabSize,e)},500)}createElementNS(e,t,r,s){var n=e?this.htmlDocument.createElementNS(e,t):this.htmlDocument.createElement(t);return Object.assign(n,r),s&&V(n,s),n}createElement(e,t,r){return this.createElementNS(void 0,e,t,r)}createSvgElement(e,t,r){return this.createElementNS(p.svg,e,t,r)}createStyleElement(e){return this.createElement("style",{innerHTML:e})}createComment(e){return this.htmlDocument.createComment(e)}later(e){this.postRenderTasks.push(e)}}function se(a){a.innerHTML=""}function V(a,e){e.forEach(t=>a.appendChild(xe(t)?document.createTextNode(t):t))}function Wt(a,e){for(var t=a.parent;t!=null&&t.type!=e;)t=t.parent;return t}const be={ignoreHeight:!1,ignoreWidth:!1,ignoreFonts:!1,breakPages:!0,debug:!1,experimental:!1,className:"docx",inWrapper:!0,hideWrapperOnPrint:!1,trimXmlDeclaration:!0,ignoreLastRenderedPageBreak:!0,renderHeaders:!0,renderFooters:!0,renderFootnotes:!0,renderEndnotes:!0,useBase64URL:!1,renderChanges:!1,renderComments:!1,renderAltChunks:!0};function Xt(a,e){const t=P(P({},be),e);return U.load(a,new Ht(t),t)}function Ut(a,e,t,r){return w(this,null,function*(){const s=P(P({},be),r);return yield new jt(window.document).render(a,e,t,s)})}function Jt(a,e,t,r){return w(this,null,function*(){const s=yield Xt(a,r);return yield Ut(s,e,t,r),s})}export{Jt as r};
