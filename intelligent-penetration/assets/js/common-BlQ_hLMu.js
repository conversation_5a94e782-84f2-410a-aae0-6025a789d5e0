var U=Object.defineProperty,B=Object.defineProperties;var _=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var D=(e,t,s)=>t in e?U(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,f=(e,t)=>{for(var s in t||(t={}))L.call(t,s)&&D(e,s,t[s]);if(x)for(var s of x(t))O.call(t,s)&&D(e,s,t[s]);return e},j=(e,t)=>B(e,_(t));var g=(e,t)=>{var s={};for(var a in e)L.call(e,a)&&t.indexOf(a)<0&&(s[a]=e[a]);if(e!=null&&x)for(var a of x(e))t.indexOf(a)<0&&O.call(e,a)&&(s[a]=e[a]);return s};var h=(e,t,s)=>new Promise((a,r)=>{var n=i=>{try{o(s.next(i))}catch(u){r(u)}},c=i=>{try{o(s.throw(i))}catch(u){r(u)}},o=i=>i.done?a(i.value):Promise.resolve(i.value).then(n,c);o((s=s.apply(e,t)).next())});import{E as F,a as I,r as q,d as P,b as v,c as M,n as H}from"./vue-core-Do3hoBJo.js";import{a as z}from"./network-crypto-CC4X1yux.js";const J=`
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,R={loadingInstance:null,open(e,t="rgba(0, 0, 0, 0.6)"){this.loadingInstance===null&&(this.loadingInstance=F.service({text:`${e||"拼命加载中..."}`,background:t,svg:J,svgViewBox:"-10, -10, 50, 50"}))},close(){this.loadingInstance!==null&&this.loadingInstance.close(),this.loadingInstance=null}};class V{constructor(){this.p=!1,this.o=!1,this.e=!1,this.c="",this.h={},this.m="",this.d={},this.s=0,this.b=void 0,this.f=void 0,this.t="",this.u="",this.up="",this.r={}}}function W(e){const t=new FormData;function s(a,r=""){a!==void 0&&(a instanceof Blob?t.append(r,a,`${r}.xlsx`):a&&typeof a=="object"&&!Array.isArray(a)?Object.keys(a).forEach(n=>{const c=r?`${r}.${n}`:n;s(a[n],c)}):Array.isArray(a)?a.forEach((n,c)=>{const o=`${r}[${c}]`;n instanceof Blob?t.append(o,n):s(n,o)}):t.append(r,a))}return s(e),t}const Q=(e,t="application/octet-stream")=>{if(!e)return t;const s={zip:"application/zip",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xls:"application/vnd.ms-excel",pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",doc:"application/msword"},a=Object.keys(s).find(r=>e.endsWith(`.${r}`)||e.includes(`.${r}`));return a?s[a]:t},G=(e,t)=>{if(!e)return t;const s=e.match(/filename="?([^"]*)"?$/);return t?s?"_"+s[1]:t:s?s[1]:""},X=(e,t)=>{if(!("download"in document.createElement("a")))return;const s=document.createElement("a");s.download=t,s.style.display="none",s.href=URL.createObjectURL(e),document.body.appendChild(s),s.click(),URL.revokeObjectURL(s.href),document.body.removeChild(s)},Y=e=>h(null,null,function*(){try{const t=yield e.text();return JSON.parse(t)}catch(t){throw t}});class Z{constructor(){this.cancelTokens=new Map}setAbortAPI(t){this.abort(t);const s=new AbortController;return this.cancelTokens.set(t,s),s.signal}abort(t){this.cancelTokens.has(t)&&(this.cancelTokens.get(t).abort(),this.cancelTokens.delete(t))}abortAll(){this.cancelTokens.forEach(t=>{t.abort()}),this.cancelTokens.clear()}}const K=new Z,ee=e=>(e!=null&&e.includes("application/octet-stream")||e!=null&&e.includes("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")||(e==null||e.includes("application/vnd.ms-excel")),e==null?void 0:e.includes("application/vnd.openxmlformats-officedocument.wordprocessingml.document")),te=e=>{e.interceptors.request.use(t=>{if(t.cancelable){const s=`${t.method}-${t.url}`;t.signal=K.setAbortAPI(s)}try{const s=ce();s.token&&(t.headers.Authentication=s.token)}catch(s){}return t.data instanceof FormData||t.headers&&t.headers["Content-Type"]==="multipart/form-data"||(t.method==="get"&&t.params,t.method==="post"&&t.data),t},t=>Promise.reject(t)),e.interceptors.response.use(t=>h(null,null,function*(){return t.data instanceof Blob&&t.data.type!=="application/json"||ee(t.headers["content-type"])||t.data instanceof Blob&&t.data.type==="application/json"&&(t.data=yield Y(t.data)),t}),t=>t.name==="AbortError"||t.code==="ERR_CANCELED"?Promise.reject({code:"REQUEST_CANCELED",message:"请求已取消"}):Promise.reject(t))},N={400:"请求参数错误",401:"未授权，请重新登录",403:"拒绝访问",404:"请求的资源不存在",405:"请求方法不允许",408:"请求超时",500:"服务器内部错误",501:"服务未实现",502:"网关错误",503:"服务不可用",504:"网关超时",505:"HTTP版本不受支持"},se=(e,t,s)=>(e.b=new Date,e.t="ajax",e.u=t,e.r=s,e.h={},e.p=!0,e.o=!1,e.e=!1,e.m="",e.d={},e.s+=1,e),ae=(e,t,s,a=!1,r=!0)=>{var n,c,o,i,u;return e.f=new Date,e.p=!1,e.c=(n=t.data)==null?void 0:n.resultCode,s?e.d=t.data:e.d=((c=t.data)==null?void 0:c.resData)!==void 0?t.data.resData:t.data,e.c==="0000"?(e.o=!0,e.e=!1,e.m=(i=(o=t.data)==null?void 0:o.resultDesc)!=null?i:"服务调用成功",e.h=t.headers,a&&I({message:e.m,type:"success"}),!0):(e.o=!1,e.e=!0,e.h=t.headers,e.m=((u=t.data)==null?void 0:u.resultDesc)||"业务处理失败",r&&I({message:e.m,type:"error"}),!1)},oe=(e,t)=>{var r,n,c,o,i,u,p;e.f=new Date,e.p=!1,e.o=!1,e.e=!0,e.c=((n=(r=t.response)==null?void 0:r.data)==null?void 0:n.resultCode)||"9999";const s=(c=t.response)==null?void 0:c.status,a=(i=(o=t.response)==null?void 0:o.data)==null?void 0:i.resultDesc;throw s&&N[s]?e.m=N[s]:a?e.m=a:e.m="网络异常，请稍后重试",I({message:e.m,type:"error"}),e.d=((p=(u=t.response)==null?void 0:u.data)==null?void 0:p.resData)||null,new Error(e.m)},E=z.create({baseURL:"/intelligent-penetration/api",timeout:1e3*60*5,headers:{"Content-Type":"application/json"}});te(E);function fe(){return q(new V)}function T(r,n,c){return h(this,arguments,function*(e,t,s,a={}){const w=a,{method:o="post",headers:i={},timeout:u,isFormData:p=!1,showSuccessMessage:b=!1,showErrorMessage:A=!0,cancelable:C=!1}=w,y=g(w,["method","headers","timeout","isFormData","showSuccessMessage","showErrorMessage","cancelable"]);se(e,t,s);try{const k=f({headers:j(f({},i),{"Content-Type":p?"multipart/form-data":"application/json"}),cancelable:C},y);u&&(k.timeout=u);const l=yield o==="get"?E.get(t,f({params:s},k)):E.post(t,p?W(s):s,k);return ae(e,l,p,b,A),e}catch(k){if(k.code==="REQUEST_CANCELED")return e.p=!1,e.o=!1,e.e=!1,e.c="CANCELED",e.d={},e.m="请求已取消",e;oe(e,k)}})}function ne(r,n){return h(this,arguments,function*(e,t,s="",a={}){const y=a,{method:c="post",loadingMessage:o="正在下载，请稍后...",contentType:i="application/octet-stream",successMessage:u="下载成功",errorMessage:p="下载失败",showLoading:b=!0,headers:A={}}=y,C=g(y,["method","loadingMessage","contentType","successMessage","errorMessage","showLoading","headers"]);b&&R.open(o,"#fff");try{const w=f({responseType:"blob",headers:A},C),k=yield c==="get"?E.get(e,f({params:t},w)):E.post(e,t,w),l=G(k.headers["content-disposition"],s),m=Q(l,i),d=new Blob([k.data],{type:m});return X(d,s+decodeURIComponent(l)),u&&I.success({message:u}),!0}catch(w){throw p&&I.error({message:p}),w}finally{b&&R.close()}})}const pe=(s,...a)=>h(null,[s,...a],function*(e,t={}){const o=t,{store:r}=o,n=g(o,["store"]);return yield T(r||{},"/user/login",e,f({showSuccessMessage:!0,showErrorMessage:!0},n))}),re=(s,...a)=>h(null,[s,...a],function*(e,t={}){const o=t,{store:r}=o,n=g(o,["store"]);return yield T(r||{},"/task/createTask",e,f({showSuccessMessage:!0},n))}),he=(...s)=>h(null,[...s],function*(e={},t={}){const c=t,{store:a}=c,r=g(c,["store"]);return yield T(a||{},"/task/queryTaskList",e,f({method:"post"},r))}),me=(s,...a)=>h(null,[s,...a],function*(e,t={}){const o=t,{store:r}=o,n=g(o,["store"]);return yield T(r||{},"/task/exec",{taskId:e},f({showSuccessMessage:!1},n))}),ke=(s,...a)=>h(null,[s,...a],function*(e,t={}){const i=t,{store:r}=i,n=g(i,["store"]);return yield T(r||{},"/task/queryTaskDetail",{taskId:e},f({method:"post"},n))}),ge=(s,...a)=>h(null,[s,...a],function*(e,t={}){const o=t,{store:r}=o,n=g(o,["store"]);return yield T(r||{},"/task/deleteTask",{taskId:e},f({showSuccessMessage:!0},n))}),ve=(s,...a)=>h(null,[s,...a],function*(e,t={}){const o=t,{store:r}=o,n=g(o,["store"]);return yield T(r||{},"/task/killTask",{taskId:e},f({showSuccessMessage:!0},n))}),we=(a,...r)=>h(null,[a,...r],function*(e,t="",s={}){return yield ne("/task/downloadReport",{taskId:e},t,f({loadingMessage:"正在下载渗透测试报告，请稍后...",successMessage:"报告下载成功",errorMessage:"报告下载失败"},s))}),le=P("task",()=>{const e=v([]),t=v(null),s=v(!1),a=v(!0),r=v(!1),n=v(null),c=M(()=>e.value.length),o=M(()=>({pending:e.value.filter(l=>l.status==="0"),running:e.value.filter(l=>l.status==="1"),completed:e.value.filter(l=>l.status==="9"),failed:e.value.filter(l=>l.status==="-1"),terminated:e.value.filter(l=>l.status==="-2")}));return{tasks:e,currentTask:t,loading:s,isCreatingNewTask:a,taskLoading:r,taskSelectionType:n,taskCount:c,tasksByStatus:o,selectTask:(l,m="SWITCH_TASK")=>{t.value=l,a.value=!1,n.value=m},clearSelection:()=>{t.value=null,a.value=!1,n.value=null},startNewTask:()=>{t.value=null,a.value=!0,n.value=null},updateTaskStatus:(l,m,d={})=>{const S=e.value.find($=>$.taskId===l);S&&(S.status=m,Object.assign(S,d),t.value&&t.value.taskId===l&&Object.assign(t.value,f({status:m},d)))},createTaskAsync:l=>h(null,null,function*(){var m;r.value=!0;try{const d=yield re(l);if(d.e||!((m=d.d)!=null&&m.taskId))throw new Error(d.m||"任务创建失败");const S={taskId:d.d.taskId,sysName:l.sysName,sysUrl:l.sysUrl,status:"1",fileName:l.fileName,presetVulnerability:l.presetVulnerability,startDate:d.d.createTime,endDate:"",reportFileName:""};return e.value.unshift(S),t.value=S,a.value=!1,n.value="NEW_TASK",S.taskId}catch(d){return t.value=null,a.value=!0,n.value=null,null}finally{r.value=!1}}),updateTaskList:l=>{e.value.splice(0,e.value.length,...l||[])},clearTaskSelectionType:()=>{n.value=null},deleteTask:l=>{const m=e.value.findIndex(d=>d.taskId===l);if(m>-1){const d=t.value&&t.value.taskId===l;return e.value.splice(m,1),d&&(t.value=null,a.value=!1),{success:!0,wasCurrentTask:d}}return{success:!1,wasCurrentTask:!1}},resetStore:()=>{e.value=[],t.value=null,a.value=!1,n.value=null,s.value=!1,r.value=!1}}}),ce=P("auth",()=>{const e=v(null),t=v(""),s=v(!1),a=M(()=>!!t.value&&!s.value);return{userInfo:e,token:t,isLoggedIn:a,isLoggingOut:s,login:o=>{t.value=o.token,e.value=o.userInfo},logout:()=>{s.value=!0;try{le().resetStore(),e.value=null,t.value="",localStorage.removeItem("intelligent-penetration-auth-store"),sessionStorage.clear();const i=[];for(let u=0;u<localStorage.length;u++){const p=localStorage.key(u);p&&p.includes("intelligent-penetration")&&i.push(p)}i.forEach(u=>localStorage.removeItem(u))}catch(o){}finally{H(()=>{s.value=!1})}},checkAuth:()=>a.value}},{persist:{key:"intelligent-penetration-auth-store",storage:localStorage,pick:["userInfo","token"]}});export{ce as a,he as b,ge as c,we as d,ke as g,pe as l,fe as n,me as s,ve as t,le as u};
